[{"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_clocks.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_clocks.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_clocks.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_common.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_common.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_common.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_delay.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_delay.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_delay.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_group_irq.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_group_irq.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_guard.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_guard.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_guard.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_io.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_io.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_io.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_irq.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_irq.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_irq.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_macl.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_macl.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_macl.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_register_protection.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_register_protection.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_rom_registers.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_sbrk.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_sbrk.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_sdram.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_sdram.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_sdram.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/bsp/mcu/all/bsp_security.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_security.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/bsp/mcu/all/bsp_security.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/r_adc/r_adc.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_adc/r_adc.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_adc/r_adc.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/r_gpt/r_gpt.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_gpt/r_gpt.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_gpt/r_gpt.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/r_ioport/r_ioport.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_ioport/r_ioport.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_ioport/r_ioport.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra/fsp/src/r_sci_uart/r_sci_uart.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_sci_uart/r_sci_uart.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/src/r_sci_uart/r_sci_uart.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra_gen/common_data.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/common_data.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/common_data.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra_gen/hal_data.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/hal_data.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/hal_data.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra_gen/main.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/main.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/main.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra_gen/pin_data.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/pin_data.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/pin_data.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/ra_gen/vector_data.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/vector_data.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen/vector_data.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/app/adc/adc_app.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/adc/adc_app.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/adc/adc_app.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/app/debug_uart/debug_uart.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/debug_uart/debug_uart.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/debug_uart/debug_uart.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/app/key/key_app.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/key/key_app.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/key/key_app.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/app/led/led_app.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/led/led_app.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/app/led/led_app.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/circular_queue/circular_queue.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/circular_queue/circular_queue.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/circular_queue/circular_queue.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/hal_entry.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/hal_entry.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/hal_entry.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/scheduler/scheduler.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/scheduler/scheduler.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/scheduler/scheduler.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/systick/systick.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/systick/systick.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/systick/systick.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/task.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/task.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/task.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/time/gpt0_timing.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/time/gpt0_timing.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/time/gpt0_timing.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "arm-none-eabi-gcc -mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/src\" -I\".\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg\" -std=c99 -c -o \"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug/src/time/gpt6_pwm.o\" -x c \"D:/Qian/e2studioa_code/Ability_Assessment_Project/src/time/gpt6_pwm.c\"", "file": "D:/Qian/e2studioa_code/Ability_Assessment_Project/src/time/gpt6_pwm.c"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "clang -I \"${workspace_loc:/${ProjName}}/script\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug\" -I\"D:\\Qian\\e2studioa_code\\Ability_Assessment_Project\\Debug\" -ferror-limit=30", "file": "D:\\Qian\\e2studioa_code\\Ability_Assessment_Project\\Debug\\memory_regions.ld"}, {"directory": "D:/Qian/e2studioa_code/Ability_Assessment_Project", "command": "clang -I \"${workspace_loc:/${ProjName}}/script\" -I\"D:/Qian/e2studioa_code/Ability_Assessment_Project/Debug\" -I\"D:\\Qian\\e2studioa_code\\Ability_Assessment_Project\\script\" -ferror-limit=30", "file": "D:\\Qian\\e2studioa_code\\Ability_Assessment_Project\\script\\fsp.ld"}]