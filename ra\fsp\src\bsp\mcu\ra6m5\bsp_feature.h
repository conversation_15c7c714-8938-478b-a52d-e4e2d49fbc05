/*
* Copyright (c) 2020 - 2025 Renesas Electronics Corporation and/or its affiliates
*
* SPDX-License-Identifier: BSD-3-Clause
*/

/***********************************************************************************************************************
 *
 * AUTOGENERATED FILE. DO NOT EDIT.
 *
 **********************************************************************************************************************/

#ifndef BSP_FEATURE_H
#define BSP_FEATURE_H

/***********************************************************************************************************************
 * Includes   <System Includes> , "Project Includes"
 **********************************************************************************************************************/

#include "bsp_peripheral.h"

/***********************************************************************************************************************
 * Macro definitions
 **********************************************************************************************************************/

/** The main oscillator drive value is based upon the oscillator frequency selected in the configuration. */
#define CGC_MAINCLOCK_DRIVE_RESERVED_MASK    (0x0U)
#if (BSP_CFG_XTAL_HZ >= (20000000))
 #define CGC_MAINCLOCK_DRIVE                 (0x0U | CGC_MAINCLOCK_DRIVE_RESERVED_MASK)
#elif (BSP_CFG_XTAL_HZ >= (16000000))
 #define CGC_MAINCLOCK_DRIVE                 (0x1U | CGC_MAINCLOCK_DRIVE_RESERVED_MASK)
#elif (BSP_CFG_XTAL_HZ >= (8000000))
 #define CGC_MAINCLOCK_DRIVE                 (0x2U | CGC_MAINCLOCK_DRIVE_RESERVED_MASK)
#else
 #define CGC_MAINCLOCK_DRIVE                 (0x3U | CGC_MAINCLOCK_DRIVE_RESERVED_MASK)
#endif

// *UNCRUSTIFY-OFF*

#define BSP_FEATURE_ACMPHS_IS_AVAILABLE                   (0UL)
#define BSP_FEATURE_ACMPHS_MIN_WAIT_TIME_US               (0UL)             // Feature not available on this device.
#define BSP_FEATURE_ACMPHS_VREF                           (0UL)             // Feature not available on this device.

#define BSP_FEATURE_ACMPLP_IS_AVAILABLE                   (0UL)
#define BSP_FEATURE_ACMPLP_HAS_COMPSEL_REGISTERS          (0UL)             // Feature not available on this device.
#define BSP_FEATURE_ACMPLP_MIN_WAIT_TIME_US               (0UL)             // Feature not available on this device.

#define BSP_FEATURE_ADC_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_ADC_ADDITION_SUPPORTED                (1UL)             // Check to see if the ADADC register is available on any ADC peripheral.
#define BSP_FEATURE_ADC_CALIBRATION_REG_AVAILABLE         (0UL)             // Check to see if the ADCALEXE register is available on any ADC peripheral.
#define BSP_FEATURE_ADC_CLOCK_SOURCE                      (FSP_PRIV_CLOCK_PCLKC)    // Clock source used for the ADC peripheral.
#define BSP_FEATURE_ADC_GROUP_B_SENSORS_ALLOWED           (1UL)             // The Extended Input Control Register (ADEXICR) controls if sensors are enabled per group.
#define BSP_FEATURE_ADC_HAS_ADBUF                         (1UL)             // Determine if the ADBUFn registers are present.
#define BSP_FEATURE_ADC_HAS_ADCER_ADPRC                   (1UL)             // Determine if the ADPRC field exists on the ADCER register.
#define BSP_FEATURE_ADC_HAS_ADCER_ADRFMT                  (1UL)             // Determine if the ADRFMT field exists on the ADCER register.
#define BSP_FEATURE_ADC_HAS_ADHVREFCNT                    (0UL)             // Determine if the ADHVREFCNT register is available.
#define BSP_FEATURE_ADC_HAS_PGA                           (0UL)             // Determine if ADPGACR is present.
#define BSP_FEATURE_ADC_HAS_SAMPLE_HOLD_REG               (0UL)             // Specifies configuration for the sample and hold circuit is available (specifically ADSHCR register).
#define BSP_FEATURE_ADC_HAS_VREFAMPCNT                    (0UL)             // Determine if VREFAMPCNT is present.
#define BSP_FEATURE_ADC_MAX_RESOLUTION_BITS               (12UL)            // Maximum ADC resolution supported.
#define BSP_FEATURE_ADC_SENSOR_MIN_SAMPLING_TIME          (4150UL)          // Minimum time, in nanoseconds, required for ADC sampling of the sensors.
#define BSP_FEATURE_ADC_SENSORS_EXCLUSIVE                 (0UL)             // Specifies that the temperature and VREF sensors are exclusive to other ADC channel operations and cannot be executed concurrently.
#define BSP_FEATURE_ADC_TSN_CALIBRATION_AVAILABLE         (1UL)             // Determine if the temperature sensor supports calibration, either factory or runtime.
#define BSP_FEATURE_ADC_TSN_CALIBRATION32_AVAILABLE       (1UL)             // Determine if TSCDR is available.
#define BSP_FEATURE_ADC_TSN_CALIBRATION32_MASK            (0xFFFFUL)        // Create the mask for the valid calibration data provided by TSCDR.
#define BSP_FEATURE_ADC_TSN_CONTROL_AVAILABLE             (1UL)             // Determine if the TSCR register is present.
#define BSP_FEATURE_ADC_TSN_SLOPE                         (4000UL)          // Typical slope for the temperature sensor, in uV/degC.
#define BSP_FEATURE_ADC_UNIT_0_CHANNELS                   (0x37FFUL)        // Mask of available channels in ADC unit 0.
#define BSP_FEATURE_ADC_UNIT_1_CHANNELS                   (0x1FFF0007UL)    // Mask of available channels in ADC unit 1.
#define BSP_FEATURE_ADC_VALID_UNIT_MASK                   (0x03UL)          // Mask of whole, physical ADC units present in the MCU.

#define BSP_FEATURE_ADC_B_IS_AVAILABLE                    (0UL)
#define BSP_FEATURE_ADC_B_PGA_CHANNEL_MASK                (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_ADC_B_PGA_SUPPORTED                   (0UL)             // Feature not available on this device.
#define BSP_FEATURE_ADC_B_TSN_CALIBRATION32_MASK          (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_ADC_B_TSN_SLOPE                       (0UL)             // Feature not available on this device.
#define BSP_FEATURE_ADC_B_UNIT_0_CHANNELS                 (0x00ULL)         // Feature not available on this device.
#define BSP_FEATURE_ADC_B_UNIT_1_CHANNELS                 (0x00ULL)         // Feature not available on this device.

#define BSP_FEATURE_ADC_D_IS_AVAILABLE                    (0UL)
#define BSP_FEATURE_ADC_D_CHANNELS                        (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_ADC_D_SCAN_MODE_CHANNELS              (0x00UL)          // Feature not available on this device.

#define BSP_FEATURE_AGT_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_AGT_AGT_CHANNEL_COUNT                 (6U)              // Number of channels for only AGT (not AGTW) peripherals.
#define BSP_FEATURE_AGT_AGTW_CHANNEL_COUNT                (0U)              // Number of channels for only AGTW (not AGT) peripherals.
#define BSP_FEATURE_AGT_USE_AGTIOSEL_ALT                  (0UL)             // Indicates use of AGTIOSEL_ALT instead of AGTIOSEL for AGTW instances.
#define BSP_FEATURE_AGT_VALID_CHANNEL_MASK                (0x3FUL)          // A mask of all valid AGTx channels.

#define BSP_FEATURE_BSP_CODE_CACHE_VERSION                (1UL)             // Version of C-Cache implemented in a CM33 core.
#define BSP_FEATURE_BSP_FLASH_CACHE                       (1UL)             // Flash cache is present.
#define BSP_FEATURE_BSP_FLASH_CACHE_DISABLE_OPM           (0UL)             // Constraints exist for flash cache operation either during power mode sequencing or flash programming access.
#define BSP_FEATURE_BSP_FLASH_PREFETCH_BUFFER             (0UL)             // Indicates the prefetch buffer is available on the flash.
#define BSP_FEATURE_BSP_HAS_ADC_CLOCK                     (0UL)             // Indicates there is a separate clock for the ADC.
#define BSP_FEATURE_BSP_HAS_CANFD_CLOCK                   (1UL)             // Indicates there is a separate clock for the CANFD.
#define BSP_FEATURE_BSP_HAS_CEC_CLOCK                     (1UL)             // Indicates there is a separate clock for the CEC.
#define BSP_FEATURE_BSP_HAS_CLOCK_SUPPLY_TYPEB            (0UL)             // Check for the ICSTATS bit field that specifies clock power architecture type.
#define BSP_FEATURE_BSP_HAS_DCDC_REGULATOR                (0UL)             // DCDCCTL register is present in SYSC.
#define BSP_FEATURE_BSP_HAS_DTCM                          (0UL)             // Indicates DTCM is available.
#define BSP_FEATURE_BSP_HAS_EXTRA_PERIPHERAL0_CLOCK       (0UL)             // Flag indicating an extra peripheral clock is present.
#define BSP_FEATURE_BSP_HAS_FSXP_CLOCK                    (0UL)             // Indicates FSXP (subsystem clock) is available.
#define BSP_FEATURE_BSP_HAS_GRAPHICS_DOMAIN               (0UL)             // Indicates that the MCU has a power domain specifically for graphics peripherals.
#define BSP_FEATURE_BSP_HAS_I3C_CLOCK                     (0UL)             // Indicates there is a separate clock for the I3C.
#define BSP_FEATURE_BSP_HAS_IIC_CLOCK                     (0UL)             // Indicates there is a separate IIC clock.
#define BSP_FEATURE_BSP_HAS_ITCM                          (0UL)             // Indicates ITCM is available.
#define BSP_FEATURE_BSP_HAS_LCD_CLOCK                     (0UL)             // Indicates there is a separate clock for the LCD.
#define BSP_FEATURE_BSP_HAS_OCTASPI_CLOCK                 (1UL)             // Indicates there is a separate clock for the OSPI.
#define BSP_FEATURE_BSP_HAS_OFS2                          (0UL)             // Indicates the OFS2 register is available.
#define BSP_FEATURE_BSP_HAS_OFS3                          (0UL)             // OSF3 register is available; currently only available for RA8.
#define BSP_FEATURE_BSP_HAS_SCE_ON_RA2                    (0UL)             // Indicates the AES peripheral is available for an RA2 device.
#define BSP_FEATURE_BSP_HAS_SCE5                          (0UL)             // Indicates the SCE5 crypto engine is available.
#define BSP_FEATURE_BSP_HAS_SCI_CLOCK                     (0UL)             // Indicates there is a separate SCI clock.
#define BSP_FEATURE_BSP_HAS_SCISPI_CLOCK                  (0UL)             // Indicates there is a separate SCI SPI clock.
#define BSP_FEATURE_BSP_HAS_SDADC_CLOCK                   (0UL)             // Indicates there is a separate clock for the SDADC.
#define BSP_FEATURE_BSP_HAS_SECURITY_MPU                  (0UL)             // Indicates the MCU has security MPU systems available.
#define BSP_FEATURE_BSP_HAS_SP_MON                        (0UL)             // Indicates the Stack Pointer monitor is available.
#define BSP_FEATURE_BSP_HAS_SPI_CLOCK                     (0UL)             // Indicates there is a separate clock for the SPI.
#define BSP_FEATURE_BSP_HAS_SYRACCR                       (0UL)             // SYRACCR register is available.
#define BSP_FEATURE_BSP_HAS_TZFSAR                        (1UL)             // Specifies the TrustZone filter can be secured.
#define BSP_FEATURE_BSP_HAS_USB_CLOCK_DIV                 (0UL)             // Indicates there is a USB clock divider setting as part of the SCKDIVCR registers.
#define BSP_FEATURE_BSP_HAS_USB_CLOCK_REQ                 (1UL)             // Indicates that a request bit must be set before changing USB clock settings.
#define BSP_FEATURE_BSP_HAS_USB_CLOCK_SEL                 (1UL)             // Indicates the USB clock has a selectable source.
#define BSP_FEATURE_BSP_HAS_USB_CLOCK_SEL_ALT             (0UL)             // Indicates the USBCKCR_ALT register should be used instead of USBCKCR.
#define BSP_FEATURE_BSP_HAS_USB60_CLOCK                   (1UL)             // Indicates the USB60 clock is available.
#define BSP_FEATURE_BSP_HAS_USBCKDIVCR                    (1UL)             // USBCKDIVCR register is available.
#define BSP_FEATURE_BSP_MCU_INFO_POINTER_LOCATION         (0x00U)           // Location of the FMIFRT register.
#define BSP_FEATURE_BSP_MMF_SUPPORTED                     (0UL)             // Memory-mirror function is available.
#define BSP_FEATURE_BSP_MPU_REGION0_MASK                  (0x00UL)          // Mask for allowed address range of the MPU.
#define BSP_FEATURE_BSP_MSTP_GPT_MSTPD5                   (0UL)             // GPT stop bits use MSTPCRD.MSTPD5.
#define BSP_FEATURE_BSP_MSTP_GPT_MSTPD5_MAX_CH            (0UL)             // Largest channel number associated with GPT on the MSTPCRD.MSTPD5 field on this MCU.
#define BSP_FEATURE_BSP_MSTP_HAS_MSTPCRE                  (1UL)             // Indicates the MSTP peripheral has an MSTPCRE register.
#define BSP_FEATURE_BSP_MSTP_POEG_MSTPD13                 (0UL)             // Indicates the MSTP uses bit 13 of MSTPCRD to control the POEG.
#define BSP_FEATURE_BSP_NUM_PMSAR                         (12UL)            // Number of available Port Security Attribution Registers.
#define BSP_FEATURE_BSP_OFS_HAS_SECURITY_ATTRIBUTION      (1UL)             // Indicates security attribution settings for banks are present in the OFS registers.
#define BSP_FEATURE_BSP_OFS1_HOCOFRQ_MASK                 (0xFFFFF9FFUL)    // Inverted mask of the HOCOFRQx bit field of the OFS1 register.
#define BSP_FEATURE_BSP_OFS1_HOCOFRQ_OFFSET               (9UL)             // Offset to the OFS1.HOCOFRQx bitfield.
#define BSP_FEATURE_BSP_OSIS_PADDING                      (0UL)             // Indicates there is 32-bits of padding between each 32-bit word of the OSIS ID registers.
#define BSP_FEATURE_BSP_POWER_CHANGE_MSTP_REQUIRED        (0UL)             // Indicates extra modules must be manually stopped before switching the system clock from the PLL.
#define BSP_FEATURE_BSP_RESET_TRNG                        (0UL)             // Specifies the TRNG must be reset after clock initialization to prevent excess current draw.
#define BSP_FEATURE_BSP_SYS_CLOCK_FREQ_FIVE_ROM_WAITS     (0UL)             // Maximum frequency allowed before requiring five wait cycles.
#define BSP_FEATURE_BSP_SYS_CLOCK_FREQ_FOUR_ROM_WAITS     (0UL)             // The maximum frequency allowed without having four ROM wait cycles.
#define BSP_FEATURE_BSP_SYS_CLOCK_FREQ_NO_RAM_WAITS       (100000000UL)     // The maximum frequency that can be used before wait cycles are necessary.
#define BSP_FEATURE_BSP_SYS_CLOCK_FREQ_ONE_ROM_WAITS      (50000000UL)      // Maximum frequency allowed before requiring one wait cycle.
#define BSP_FEATURE_BSP_SYS_CLOCK_FREQ_THREE_ROM_WAITS    (150000000UL)     // Maximum frequency allowed before requiring three wait cycles.
#define BSP_FEATURE_BSP_SYS_CLOCK_FREQ_TWO_ROM_WAITS      (100000000UL)     // Maximum frequency allowed before requiring two wait cycles.
#define BSP_FEATURE_BSP_UNIQUE_ID_OFFSET                  (0x00UL)          // Bit offset of the Unique ID in the mcu info block.
#define BSP_FEATURE_BSP_UNIQUE_ID_POINTER                 (0x01008190UL)    // Address of the MCU Unique ID register (UIDR).
#define BSP_FEATURE_BSP_VBATT_HAS_VBTCR1_BPWSWSTP         (0UL)             // VCC can switch to VBAT if the voltage drops too low.

#define BSP_FEATURE_CAN_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_CAN_CHECK_PCLKB_RATIO                 (0UL)             // Feature not available on this device.
#define BSP_FEATURE_CAN_CLOCK                             (0UL)             // Feature not available on this device.
#define BSP_FEATURE_CAN_MCLOCK_ONLY                       (0UL)             // Feature not available on this device.
#define BSP_FEATURE_CAN_NUM_CHANNELS                      (0UL)             // Feature not available on this device.

#define BSP_FEATURE_CANFD_IS_AVAILABLE                    (1UL)
#define BSP_FEATURE_CANFD_FD_SUPPORT                      (BSP_MCU_FEATURE_SET == 'B')  // Flexible data rate support.
#define BSP_FEATURE_CANFD_LITE                            (0UL)             // CANFD Lite or CANFD_B is the standard CAN peripheral for new designs.
#define BSP_FEATURE_CANFD_NUM_CHANNELS                    (2UL)             // Number of CANFD channels per CANFD peripheral instance.
#define BSP_FEATURE_CANFD_NUM_INSTANCES                   (1UL)             // Number of hardware instances of the CANFD peripheral.

#define BSP_FEATURE_CGC_EXECUTE_FROM_LOCO                 (1UL)             // Indicates the system clock can be sourced by the LOCO.
#define BSP_FEATURE_CGC_HAS_BCLK                          (1UL)             // External Bus Clock is available.
#define BSP_FEATURE_CGC_HAS_CPUCLK                        (0UL)             // CPU Clock is available.
#define BSP_FEATURE_CGC_HAS_EXTRACLK2                     (0UL)             // System contains an extra clock domain.
#define BSP_FEATURE_CGC_HAS_FCLK                          (1UL)             // FlashIF clock is available.
#define BSP_FEATURE_CGC_HAS_FLDWAITR                      (0UL)             // FLDWAITR register is available.
#define BSP_FEATURE_CGC_HAS_FLL                           (1UL)             // FLL is available.
#define BSP_FEATURE_CGC_HAS_FLWT                          (1UL)             // FLWT register is available.
#define BSP_FEATURE_CGC_HAS_HOCOWTCR                      (0UL)             // HOCOWTCR register is available.
#define BSP_FEATURE_CGC_HAS_MEMWAIT                       (0UL)             // MEMWAIT register is available.
#define BSP_FEATURE_CGC_HAS_OSTDCSE                       (0UL)             // OSTDCSE register is available.
#define BSP_FEATURE_CGC_HAS_PCLKA                         (1UL)             // Peripheral module clock A is available.
#define BSP_FEATURE_CGC_HAS_PCLKB                         (1UL)             // Peripheral module clock B is available.
#define BSP_FEATURE_CGC_HAS_PCLKC                         (1UL)             // Peripheral module clock C is available.
#define BSP_FEATURE_CGC_HAS_PCLKD                         (1UL)             // Peripheral module clock D is available.
#define BSP_FEATURE_CGC_HAS_PCLKE                         (0UL)             // Peripheral module clock E is available.
#define BSP_FEATURE_CGC_HAS_PLL                           (1UL)             // PLL is available.
#define BSP_FEATURE_CGC_HAS_PLL2                          (1UL)             // PLL2 is available.
#define BSP_FEATURE_CGC_HAS_PLLRTC                        (0UL)             // PLLRTC is available.
#define BSP_FEATURE_CGC_HAS_SOPCCR                        (1UL)             // SOPCCR register is available.
#define BSP_FEATURE_CGC_HAS_SOSC                          (1UL)             // Sub-clock oscillator is available.
#define BSP_FEATURE_CGC_HAS_SRAMPRCR2                     (1UL)             // SRAMPRCR2 register is available.
#define BSP_FEATURE_CGC_HAS_SRAMWTSC                      (1UL)             // SRAM Wait State Control Register is available.
#define BSP_FEATURE_CGC_HOCOSF_BEFORE_OPCCR               (0UL)             // Changes to OPCCR must only occur with HOCO is stopped or stable.
#define BSP_FEATURE_CGC_HOCOWTCR_64MHZ_ONLY               (0UL)             // HOCO wait control register changes value for 64 MHz speed.
#define BSP_FEATURE_CGC_HOCOWTCR_SCI_SNOOZE_VALUE         (0UL)             // HOCO stabilization wait time when using SCI0.
#define BSP_FEATURE_CGC_HOCOWTCR_VALUE                    (0UL)             // HOCO stabilization wait time register value for 64 MHz.
#define BSP_FEATURE_CGC_ICLK_DIV_RESET                    (BSP_CLOCKS_SYS_CLOCK_DIV_4)  // Reset value of the ICLK divider.
#define BSP_FEATURE_CGC_LOCO_STABILIZATION_MAX_US         (61UL)            // LOCO stabilization time in microseconds.
#define BSP_FEATURE_CGC_LOW_SPEED_MAX_FREQ_HZ             (1000000UL)       // Maximum frequency during low-speed operation.
#define BSP_FEATURE_CGC_LOW_SPEED_SUPPORT_MAIN_OSC        (1UL)             // The main clock oscillator is available in low-speed mode.
#define BSP_FEATURE_CGC_LOW_VOLTAGE_MAX_FREQ_HZ           (0UL)             // Maximum frequency during low-voltage mode.
#define BSP_FEATURE_CGC_MIDDLE_SPEED_MAX_FREQ_HZ          (0UL)             // Middle speed clock maximum frequency.
#define BSP_FEATURE_CGC_MOCO_STABILIZATION_MAX_US         (15UL)            // MOCO stabilization time in microseconds.
#define BSP_FEATURE_CGC_MODRV_MASK                        (R_SYSTEM_MOMCR_MODRV0_Msk | CGC_MAINCLOCK_DRIVE_RESERVED_MASK)   // Mask used on MODRV register.
#define BSP_FEATURE_CGC_MODRV_SHIFT                       (R_SYSTEM_MOMCR_MODRV0_Pos)   // Shift used for MODRV register.
#define BSP_FEATURE_CGC_OSCILLATON_STOP_DETECT            (1UL)             // Oscillation stop detection is available.
#define BSP_FEATURE_CGC_PLL_HOCO_MAX_CPUCLK_HZ            (0UL)             // Maximum allowed clock speed when HOCO is the PLL source clock for the CPUCLK.
#define BSP_FEATURE_CGC_PLL_OUT_MAX_HZ                    (200000000UL)     // Maximum output frequency for PLL unit 1.
#define BSP_FEATURE_CGC_PLL_OUT_MIN_HZ                    (120000000UL)     // Minimum output frequency for PLL unit 1.
#define BSP_FEATURE_CGC_PLL_REFERENCE_CLK_MAX_HZ          (0UL)             // Maximum frequency of the PLL reference clock.
#define BSP_FEATURE_CGC_PLL_REFERENCE_CLK_MIN_HZ          (0UL)             // Minimum frequency of the PLL reference clock.
#define BSP_FEATURE_CGC_PLL_SRC_MAX_HZ                    (24000000UL)      // Maximum input frequency for PLL unit 1.
#define BSP_FEATURE_CGC_PLL_SRC_MIN_HZ                    (8000000UL)       // Minimum output frequency for PLL unit 1.
#define BSP_FEATURE_CGC_PLL1_NUM_OUTPUT_CLOCKS            (1UL)             // Number of output clocks for PLL1.
#define BSP_FEATURE_CGC_PLL2_NUM_OUTPUT_CLOCKS            (1UL)             // Number of output clocks for PLL2.
#define BSP_FEATURE_CGC_PLL2_OUT_MAX_HZ                   (240000000UL)     // Maximum output frequency for PLL unit 2.
#define BSP_FEATURE_CGC_PLL2_OUT_MIN_HZ                   (120000000UL)     // Minimum output frequency for PLL unit 2.
#define BSP_FEATURE_CGC_PLLCCR_TYPE                       (1UL)             // Indicates the type of PLLCCR register and PLL.
#define BSP_FEATURE_CGC_PLLCCR_VCO_MAX_HZ                 (200000000UL)     // PLL VCO maximum frequency.
#define BSP_FEATURE_CGC_PLLCCR_VCO_MIN_HZ                 (0UL)             // PLL VCO minimum frequency.
#define BSP_FEATURE_CGC_PLLCCR_WAIT_US                    (0UL)             // Time required, in microseconds, between changing PLLCCR.PLLMUL to clearing PLLCR.PLLSTP.
#define BSP_FEATURE_CGC_REGISTER_SET_B                    (0UL)             // Clock generation uses an alternative register set.
#define BSP_FEATURE_CGC_SCKDIVCR_BCLK_MATCHES_PCLKB       (0UL)             // Requires the SCKDIVCR.BCLK bits [18:16] to match SCKDIBCR.PCLKB.
#define BSP_FEATURE_CGC_SCKDIVCR2_HAS_EXTRA_CLOCKS        (0UL)             // Indicates the SCKDIVCR2 register has additional clocks.
#define BSP_FEATURE_CGC_SODRV_MASK                        (0x02UL)          // Sub-clock drive field mask.
#define BSP_FEATURE_CGC_SODRV_SHIFT                       (1UL)             // Sub-clock drive field shift.
#define BSP_FEATURE_CGC_SRAMPRCR_KW_OFFSET                (1UL)             // Bit offset for SRAMPRCR.KW field.
#define BSP_FEATURE_CGC_SRAMPRCR_KW_VALUE                 (0x78U)           // Write enable key code for SRAMPRCR bit.
#define BSP_FEATURE_CGC_STARTUP_OPCCR_MODE                (0x00UL)          // Reset value for the OPCCR regsiter.
#define BSP_FEATURE_CGC_STARTUP_SCKDIVCR                  (0x22022222UL)    // Reset value for the SCKDIVCR register.
#define BSP_FEATURE_CGC_STARTUP_SCKDIVCR2                 (0x00UL)          // Reset value for the SCKDIVCR2 register.
#define BSP_FEATURE_CGC_STARTUP_SCKSCR                    (0x01UL)          // Reset value for the SCKSCR register.

#define BSP_FEATURE_CRC_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_CRC_HAS_CRCCR0_LMS                    (1UL)             // The CRC peripheral supports both LSB- and MSB-first calculations.
#define BSP_FEATURE_CRC_HAS_SNOOP                         (0UL)             // The CRC peripheral can snoop on (monitor a) SCI data register for data to checksum.
#define BSP_FEATURE_CRC_POLYNOMIAL_MASK                   (0x3EU)           // Mask of available CRC polynomials; should match the mask of indexes relating to r_crc_api.h::crc_polynomial_t.
#define BSP_FEATURE_CRC_SNOOP_ADDRESS_TYPE_TDR            (0x00UL)          // Used to indicate the type of register being snooped on; derived from the least-significant nybble of the address of SCI TDR registers.

#define BSP_FEATURE_CRYPTO_HAS_AES                        (1UL)             // AES support is available.
#define BSP_FEATURE_CRYPTO_HAS_AES_WRAPPED                (1UL)             // AES support with key-wrapping is available.
#define BSP_FEATURE_CRYPTO_HAS_CTR_DRBG                   (1UL)             // AES CTR-DRBG pseudo random number support is available.
#define BSP_FEATURE_CRYPTO_HAS_ECC                        (1UL)             // ECC support is available.
#define BSP_FEATURE_CRYPTO_HAS_ECC_WRAPPED                (1UL)             // ECC support with key-wrapping is available.
#define BSP_FEATURE_CRYPTO_HAS_HASH                       (1UL)             // Hashing support is available.
#define BSP_FEATURE_CRYPTO_HAS_RSA                        (1UL)             // RSA support is available.
#define BSP_FEATURE_CRYPTO_HAS_RSA_WRAPPED                (1UL)             // RSA support with key-wrapping is available.

#define BSP_FEATURE_CTSU_IS_AVAILABLE                     (1UL)
#define BSP_FEATURE_CTSU_CTSUCHAC_REGISTER_COUNT          (3UL)             // Number of CTSUCHAC registers.
#define BSP_FEATURE_CTSU_CTSUCHTRC_REGISTER_COUNT         (3UL)             // Number of CTSUCHTRC registers.
#define BSP_FEATURE_CTSU_HAS_TXVSEL                       (1UL)             // CTSUCR0.CTSUTXVSEL field is available.
#define BSP_FEATURE_CTSU_VERSION                          (1UL)             // Version of the CTSU peripheral.

#define BSP_FEATURE_DAC_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_DAC_AD_SYNC_UNIT_MASK                 (0x02UL)          // DAADSCR register is available.
#define BSP_FEATURE_DAC_B_CHANNELS_PER_UNIT               (0UL)             // Number of available channels per DAC_B instance.
#define BSP_FEATURE_DAC_B_UNIT_COUNT                      (0UL)             // Number of available DAC_B instance.
#define BSP_FEATURE_DAC_HAS_CHARGEPUMP                    (0UL)             // DAPC register is available.
#define BSP_FEATURE_DAC_HAS_DA_AD_SYNCHRONIZE             (1UL)             // At least one channel supports A/D synchronization with the DAC.
#define BSP_FEATURE_DAC_HAS_DAVREFCR                      (0UL)             // DAVREFCR register is available.
#define BSP_FEATURE_DAC_HAS_INTERNAL_OUTPUT               (0UL)             // DAC output can be routed to specific extra internal modules.
#define BSP_FEATURE_DAC_HAS_OUTPUT_AMPLIFIER              (1UL)             // DAAMPCR register is available.

#define BSP_FEATURE_DAC8_IS_AVAILABLE                     (0UL)
#define BSP_FEATURE_DAC8_CHANNELS_PER_UNIT                (0UL)             // Feature not available on this device.
#define BSP_FEATURE_DAC8_HAS_CHARGEPUMP                   (0UL)             // Feature not available on this device.
#define BSP_FEATURE_DAC8_HAS_DA_AD_SYNCHRONIZE            (0UL)             // Feature not available on this device.
#define BSP_FEATURE_DAC8_HAS_REALTIME_MODE                (0UL)             // Feature not available on this device.
#define BSP_FEATURE_DAC8_UNIT_COUNT                       (0UL)             // Feature not available on this device.

#define BSP_FEATURE_DAC12_IS_AVAILABLE                    (1UL)
#define BSP_FEATURE_DAC12_CHANNELS_PER_UNIT               (2UL)             // Number of available channels per DAC12 instance.
#define BSP_FEATURE_DAC12_UNIT_COUNT                      (1UL)             // Number of available DAC12 instance.

#define BSP_FEATURE_DMAC_IS_AVAILABLE                     (1UL)
#define BSP_FEATURE_DMAC_HAS_DELSR                        (0UL)             // DELSRn registers are available in the DMA peripheral block.
#define BSP_FEATURE_DMAC_HAS_DMCTL                        (0UL)             // DMCTL register is available in the DMA peripheral block.
#define BSP_FEATURE_DMAC_HAS_REPEAT_BLOCK_MODE            (1UL)             // DMTMD register's MD bit-field allows repeat-block transfers (value: 0b11).
#define BSP_FEATURE_DMAC_MAX_CHANNEL                      (8UL)             // Number of DMAC channels available.

#define BSP_FEATURE_DOC_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_DOC_VERSION                           (1UL)             // The version of the DOC peripheral.

#define BSP_FEATURE_DTC_TRANSFER_INFO_ALIGNMENT           (4UL)             // Byte alignment that must be used for DTC transfer info structs.

#define BSP_FEATURE_DWT_CYCCNT                            (1UL)             // CYCNT register is available on CM33 and higher devices.

#define BSP_FEATURE_ELC_VERSION                           (1UL)             // Version of the ELC peripheral.

#define BSP_FEATURE_ESC_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_ESC_MAX_PORTS                         (0UL)             // Feature not available on this device.

#define BSP_FEATURE_ETHER_IS_AVAILABLE                    (1UL)
#define BSP_FEATURE_ETHER_FIFO_DEPTH                      (0x070FUL)        // Valid value of EDMACn.FDR register.
#define BSP_FEATURE_ETHER_MAX_CHANNELS                    (1UL)             // Number of available ethernet PHYs.
#define BSP_FEATURE_ETHER_MAX_QUEUE_NUM                   (0UL)             // The number of AXI bus descriptors available to Ethernet components.
#define BSP_FEATURE_ETHER_SUPPORTS_TZ_SECURE              (0UL)             // Whether or not the ETHERC peripheral supports TrustZone secure access.

#define BSP_FEATURE_FLASH_ARC_NSEC_MULTIPLE_MAX_COUNT     (0UL)             // Number of bits per counter when ARC_NSEC is configured as multiple counters.
#define BSP_FEATURE_FLASH_ARC_NSEC_NUM_COUNTERS           (0L)              // Number of non-secure application anti-rollback counters that can be configured.
#define BSP_FEATURE_FLASH_ARC_NSEC_SINGLE_MAX_COUNT       (0UL)             // Number of counter bits available when using the ARC_NSEC counter as a single, large counter.
#define BSP_FEATURE_FLASH_ARC_OEMBL_MAX_COUNT             (0UL)             // Number of counter bits for the ARC_OEMBL counter.
#define BSP_FEATURE_FLASH_ARC_SEC_MAX_COUNT               (0UL)             // Number of counter bits for the ARC_SEC counter.
#define BSP_FEATURE_FLASH_CODE_FLASH_START                (0x00UL)          // Start address of the Code Flash region.
#define BSP_FEATURE_FLASH_DATA_FLASH_START                (0x08000000UL)    // Start address of the Data Flash region.
#define BSP_FEATURE_FLASH_SUPPORTS_ACCESS_WINDOW          (0UL)             // Flash supports protected access window (AWS register is available).
#define BSP_FEATURE_FLASH_SUPPORTS_ANTI_ROLLBACK          (0UL)             // Flash supports anti-rollback counter (ARC_* registers are available).
#define BSP_FEATURE_FLASH_SUPPORTS_ID_CODE                (0UL)             // ID code is supported (OSIS register is available).
#define BSP_FEATURE_FLASH_USER_LOCKABLE_AREA_SIZE         (0UL)             // Size of the user lockable areas (non-OFS registers).
#define BSP_FEATURE_FLASH_USER_LOCKABLE_AREA_START        (0x00UL)          // Start address of the first non-OFS lockable word by LK_CD_A0.

#define BSP_FEATURE_FLASH_HP_IS_AVAILABLE                 (1UL)
#define BSP_FEATURE_FLASH_HP_CF_DUAL_BANK_START           (0x00200000UL)    // Start of the second code flash bank.
#define BSP_FEATURE_FLASH_HP_CF_REGION0_BLOCK_SIZE        (0x2000UL)        // Block size of region 0.
#define BSP_FEATURE_FLASH_HP_CF_REGION0_SIZE              (0x00010000UL)    // Size of region 0.
#define BSP_FEATURE_FLASH_HP_CF_REGION1_BLOCK_SIZE        (0x8000UL)        // Block size of region 1.
#define BSP_FEATURE_FLASH_HP_CF_WRITE_SIZE                (128UL)           // Write size for code flash.
#define BSP_FEATURE_FLASH_HP_DF_BLOCK_SIZE                (64UL)            // Block size of data flash.
#define BSP_FEATURE_FLASH_HP_DF_WRITE_SIZE                (4UL)             // Write size for data flash.
#define BSP_FEATURE_FLASH_HP_HAS_BANKSEL                  (1UL)             // BANKSEL, BANKSEL_SEC and BANKSEL_SEL registers are present.
#define BSP_FEATURE_FLASH_HP_HAS_FMEPROT                  (1UL)             // FMEPROT register is present.
#define BSP_FEATURE_FLASH_HP_SUPPORTS_DUAL_BANK           (1UL)             // Device contains two code banks.
#define BSP_FEATURE_FLASH_HP_VERSION                      (40UL)            // Version of the FLASH_HP (FACI) peripheral/hardware.

#define BSP_FEATURE_FLASH_LP_IS_AVAILABLE                 (0UL)
#define BSP_FEATURE_FLASH_LP_AWS_FAW_MASK                 (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_AWS_FAW_SHIFT                (0UL)             // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_CF_BLOCK_SIZE                (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_CF_DUAL_BANK_START           (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_CF_WRITE_SIZE                (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_DF_BLOCK_SIZE                (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_DF_WRITE_SIZE                (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_FLASH_CLOCK_SRC              (0)               // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_SUPPORTS_DUAL_BANK           (0UL)             // Feature not available on this device.
#define BSP_FEATURE_FLASH_LP_VERSION                      (0UL)             // Feature not available on this device.

#define BSP_FEATURE_GPT_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_GPT_32BIT_CHANNEL_MASK                (0x0FUL)          // Mask of 32-bit GPT channel indices.
#define BSP_FEATURE_GPT_AD_DIRECT_START_CHANNEL_MASK      (0x00UL)          // Mask of GPT channels supporting A/D conversion start.
#define BSP_FEATURE_GPT_AD_DIRECT_START_SUPPORTED         (0UL)             // At least one GPT channel with A/D conversion start is available.
#define BSP_FEATURE_GPT_CLOCK_DIVIDER_STEP_SIZE           (2UL)             // Multiplicative step size of the clock divider (GTCR.TPCS).
#define BSP_FEATURE_GPT_CLOCK_DIVIDER_VALUE_7_9_VALID     (0UL)             // Whether or not the bit-values of 0b0111 and 0b1001 are valid divider settings (GTCR.TPCS).
#define BSP_FEATURE_GPT_EVENT_COUNT_CHANNEL_MASK          (0x03FFUL)        // Mask of channels that support event count input (has GTUPSR register).
#define BSP_FEATURE_GPT_EVENT_COUNT_SUPPORTED             (1UL)             // At least one channel supports event counts.
#define BSP_FEATURE_GPT_GPTE_CHANNEL_MASK                 (0x00UL)          // Mask of GPT channels that are the GPTE implementation.
#define BSP_FEATURE_GPT_GPTE_SUPPORTED                    (0UL)             // At least one GPTE implementation is available.
#define BSP_FEATURE_GPT_GPTEH_CHANNEL_MASK                (0x00UL)          // Mask of GPT channels that are the GPTEH implementation.
#define BSP_FEATURE_GPT_GPTEH_SUPPORTED                   (0UL)             // At least one GPTEH implementation is available.
#define BSP_FEATURE_GPT_GTDVU_CHANNEL_MASK                (0x03FFUL)        // Mask of channels that support dead time control.
#define BSP_FEATURE_GPT_GTDVU_SUPPORTED                   (1UL)             // At least one GPT channel with GTDVU support is available.
#define BSP_FEATURE_GPT_ODC_128_RESOLUTION_CHANNEL_MASK   (0x00UL)          // Mask of PWM channels which support 128-bit delay resolution.
#define BSP_FEATURE_GPT_ODC_128_RESOLUTION_SUPPORTED      (0UL)             // The PWM delay circuit supports 128-bit resolution for delays.
#define BSP_FEATURE_GPT_ODC_FRANGE_FREQ_MIN               (0UL)             // Minimum frequency for standard PDG operation, must set GTCLYCR.FRANGE bit below this value.
#define BSP_FEATURE_GPT_ODC_FRANGE_SET_BIT(gpt_frequency) (0UL)             // Obtains the set bit based on the GPT frequency and the FRANGE threshold.
#define BSP_FEATURE_GPT_ODC_FREQ_MAX                      (0UL)             // Maximum supported frequency of the PWM Delay Generation circuit.
#define BSP_FEATURE_GPT_ODC_FREQ_MIN                      (0UL)             // Minimum supported frequency of the PWM Delay Generation circuit.
#define BSP_FEATURE_GPT_OPS_CHANNEL_MASK                  (0x01UL)          // Mask of channels supporting output phase switching.
#define BSP_FEATURE_GPT_OPS_SUPPORTED                     (1UL)             // At least one GPT channel with OPS support is available.
#define BSP_FEATURE_GPT_TPCS_SHIFT                        (0UL)             // Shift value to convert TPCS bit values to real multiplicative values.

#define BSP_FEATURE_I3C_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_I3C_HAS_HDR_MODE                      (0UL)             // Feature not available on this device.
#define BSP_FEATURE_I3C_MAX_DEV_COUNT                     (0UL)             // Feature not available on this device.
#define BSP_FEATURE_I3C_MSTP_OFFSET                       (0UL)             // Feature not available on this device.
#define BSP_FEATURE_I3C_NTDTBP0_DEPTH                     (0UL)             // Feature not available on this device.
#define BSP_FEATURE_I3C_NUM_CHANNELS                      (0UL)             // Feature not available on this device.

#define BSP_FEATURE_ICU_FIXED_IELSR_COUNT                 (0UL)             // Number of IELSRn registers that have a fixed event source.
#define BSP_FEATURE_ICU_HAS_FILTER                        (1UL)             // ICU contains digital input filtering.
#define BSP_FEATURE_ICU_HAS_IELSR                         (1UL)             // ICU Event Link is available.
#define BSP_FEATURE_ICU_HAS_INTERRUPT_GROUPS              (0UL)             // Indicates that event links are grouped with multiple sources.
#define BSP_FEATURE_ICU_HAS_LOCO_FILTER                   (0UL)             // Register IRQCR has LOCOSEL.
#define BSP_FEATURE_ICU_HAS_WUPEN1                        (1UL)             // WUPEN1 register is available.
#define BSP_FEATURE_ICU_HAS_WUPEN2                        (0UL)             // WUPEN2 register is available.
#define BSP_FEATURE_ICU_IRQ_CHANNELS_MASK                 (0xFFFFUL)        // Mask of available IRQ control registers.
#define BSP_FEATURE_ICU_NMIER_MAX_INDEX                   (15UL)            // Maximum bit field index of valid fields of the NMIER register.
#define BSP_FEATURE_ICU_SBYEDCR_MASK                      (0x00ULL)         // A mask of valid bits for [SBYEDCR1:SBYEDCR0].
#define BSP_FEATURE_ICU_WUPEN_MASK                        (0x00000007FF0DFFFFULL)   // A mask of valid bits for [WUPEN1:WUPEN0].

#define BSP_FEATURE_IIC_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_IIC_B_CHECK_SCL_STATUS                (0UL)             // SCL status needs to be checked before writing the transmission data in master mode.
#define BSP_FEATURE_IIC_B_FAST_MODE_PLUS                  (0x00UL)          // Mask of channels which support "Fast Mode Plus": up to 1 Mbps bit rates.
#define BSP_FEATURE_IIC_B_VALID_CHANNEL_MASK              (0x00UL)          // Mask of available IIC_B or compatible I3C channels.
#define BSP_FEATURE_IIC_BUS_FREE_TIME_MULTIPLIER          (0UL)             // Multiplication factor to calculate SDA bus free time.
#define BSP_FEATURE_IIC_FAST_MODE_PLUS                    (1UL)             // Mask of channels which support "Fast Mode Plus": up to 1 Mbps bit rates.
#define BSP_FEATURE_IIC_VALID_CHANNEL_MASK                (0x07UL)          // Mask of available IIC channels.

#define BSP_FEATURE_IOPORT_ELC_PORTS                      (0x1EUL)          // Mask of valid indices for ELC signal mapping of port input data.
#define BSP_FEATURE_IOPORT_VERSION                        (1UL)             // Version of the system PFS block.

#define BSP_FEATURE_IWDT_IS_AVAILABLE                     (1UL)
#define BSP_FEATURE_IWDT_CLOCK_FREQUENCY                  (15000UL)         // Frequency of the independent watchdog clock source.
#define BSP_FEATURE_IWDT_SUPPORTS_REGISTER_START_MODE     (0UL)             // IWDT peripheral supports register start mode.

#define BSP_FEATURE_KINT_IS_AVAILABLE                     (0UL)
#define BSP_FEATURE_KINT_HAS_MSTP                         (0UL)             // Feature not available on this device.

#define BSP_FEATURE_LPM_CHANGE_MSTP_ARRAY                 {}                // An array of tuples (MSTP index, bit) that indicate which modules must enter the stop state before the system enters low power mode or when changes to SCKDIVCR are made.
#define BSP_FEATURE_LPM_CHANGE_MSTP_REQUIRED              (0UL)             // Indicates some modules must be explicitly stopped before entering low power modes or changing SCKDIVCR.
#define BSP_FEATURE_LPM_DPSIEGR_MASK                      (0x0013FFFFULL)   // Mask of valid bit-fields of the DPSIEGRn registers.
#define BSP_FEATURE_LPM_DPSIER_MASK                       (0x0F1FFFFFULL)   // Mask of valid bit-fields of the DPSIERn registers.
#define BSP_FEATURE_LPM_HAS_DEEP_SLEEP                    (0UL)             // The device supports deep sleep mode.
#define BSP_FEATURE_LPM_HAS_DEEP_STANDBY                  (1UL)             // The device supports deep standby mode.
#define BSP_FEATURE_LPM_HAS_DPSBYCR_DEEPCUT               (1UL)             // The DPSBYCR.DEEPCUT field is available.
#define BSP_FEATURE_LPM_HAS_DPSBYCR_DPSBY                 (1UL)             // The DPSBYCR.DPSBY field is available.
#define BSP_FEATURE_LPM_HAS_DPSBYCR_SRKEEP                (0UL)             // The DPSBYCR.SRKEEP field is available.
#define BSP_FEATURE_LPM_HAS_DPSIEGR3                      (0UL)             // The DPSIEGR3 register is available.
#define BSP_FEATURE_LPM_HAS_DPSIEGR4                      (0UL)             // The DPSIEGR4 register is available.
#define BSP_FEATURE_LPM_HAS_DPSIER4                       (0UL)             // The DPSIER4 register is available.
#define BSP_FEATURE_LPM_HAS_DPSIER5                       (0UL)             // The DPSIER5 register is available.
#define BSP_FEATURE_LPM_HAS_FLASH_MODE_SELECT             (0UL)             // The SBYCR.FLSTP field is available.
#define BSP_FEATURE_LPM_HAS_HOCO_STARTUP_SPEED_MODE       (0UL)             // The SBYCR.FWKUP field is available.
#define BSP_FEATURE_LPM_HAS_LDO_CONTROL                   (0UL)             // LDOs for clock sources can be enabled/disabled.
#define BSP_FEATURE_LPM_HAS_LPSCR                         (0UL)             // The LPSCR register is available.
#define BSP_FEATURE_LPM_HAS_PDRAMSCR                      (0UL)             // The PDRAMSCRn registers are available.
#define BSP_FEATURE_LPM_HAS_SBYCR_OPE                     (1UL)             // The SBYCR.OPE field is available.
#define BSP_FEATURE_LPM_HAS_SBYCR_SSBY                    (1UL)             // The SBYCR.SSBY field is available.
#define BSP_FEATURE_LPM_HAS_SNOOZE                        (1UL)             // The MCU supports Snooze.
#define BSP_FEATURE_LPM_HAS_SNZEDCR1                      (1UL)             // The SNZEDCR1 register is available.
#define BSP_FEATURE_LPM_HAS_SNZREQCR1                     (1UL)             // The SNZREQCR1 register is available.
#define BSP_FEATURE_LPM_HAS_STANDBY_SOSC_SELECT           (0UL)             // The SBYCR.RTCLPC field is available.
#define BSP_FEATURE_LPM_HAS_STCONR                        (0UL)             // The STCONR register is available.
#define BSP_FEATURE_LPM_RTC_REGISTER_CLOCK_DISABLE        (0UL)             // RTC registers' clock should be disabled for additional power savings in LPM.
#define BSP_FEATURE_LPM_SBYCR_WRITE1_B14                  (0UL)             // Indicates that bit 14 of the SBYCR register should always be set.
#define BSP_FEATURE_LPM_SNZEDCR_MASK                      (0x01FFUL)        // Mask of valid bits for the SNZEDCRn registers.
#define BSP_FEATURE_LPM_SNZREQCR_MASK                     (0x000000077300FFFFULL)   // Mask of valid bits for the SNZREQCRn registers.
#define BSP_FEATURE_LPM_STANDBY_MOCO_REQUIRED             (0UL)             // The Middle-speed On-Chip Oscillator must be operating prior to entering standby mode.
#define BSP_FEATURE_LPM_STANDBY_MODE_CLEAR_DTCST          (0UL)             // DTCST register must be cleared prior to entering standby mode.

#define BSP_FEATURE_LVD_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_LVD_EXLVD_STABILIZATION_TIME_US       (0UL)             // Detection delay time for EXLVD pin input.
#define BSP_FEATURE_LVD_EXLVDVBAT_HI_THRESHOLD            ((lvd_threshold_t) 0U)    // External LVD for VBAT reference voltage high threshold.
#define BSP_FEATURE_LVD_EXLVDVBAT_LOW_THRESHOLD           ((lvd_threshold_t) 0U)    // External LVD for VBAT reference voltage low threshold.
#define BSP_FEATURE_LVD_EXLVDVRTC_HI_THRESHOLD            ((lvd_threshold_t) 0U)    // External LVD for VRTC reference voltage high threshold.
#define BSP_FEATURE_LVD_EXLVDVRTC_LOW_THRESHOLD           ((lvd_threshold_t) 0U)    // External LVD for VRTC reference voltage low threshold.
#define BSP_FEATURE_LVD_HAS_DIGITAL_FILTER                (1UL)             // Digital input filtering is available.
#define BSP_FEATURE_LVD_HAS_EXT_MONITOR                   (0UL)             // Voltage monitoring is available for an external power supply via pin.
#define BSP_FEATURE_LVD_HAS_LVDLVLR                       (0UL)             // LVDLVLR register is available.
#define BSP_FEATURE_LVD_MONITOR_1_HI_THRESHOLD            (LVD_THRESHOLD_MONITOR_1_LEVEL_2_99V) // Typical higher bound of the detection threshold for LVD1.
#define BSP_FEATURE_LVD_MONITOR_1_LOW_THRESHOLD           (LVD_THRESHOLD_MONITOR_1_LEVEL_2_85V) // Typical lower bound of the detection threshold for LVD1.
#define BSP_FEATURE_LVD_MONITOR_1_STABILIZATION_TIME_US   (10UL)            // Maximum stabilization time to wait after LVD1 is enabled.
#define BSP_FEATURE_LVD_MONITOR_2_HI_THRESHOLD            (LVD_THRESHOLD_MONITOR_2_LEVEL_2_99V) // Typical higher bound of the detection threshold for LVD2.
#define BSP_FEATURE_LVD_MONITOR_2_LOW_THRESHOLD           (LVD_THRESHOLD_MONITOR_2_LEVEL_2_85V) // Typical lower bound of the detection threshold for LVD2.
#define BSP_FEATURE_LVD_MONITOR_2_STABILIZATION_TIME_US   (10UL)            // Maximum stabilization time to wait after LVD2 is enabled.
#define BSP_FEATURE_LVD_MONITOR_MASK                      (0x03UL)          // Mask of programmable monitors.
#define BSP_FEATURE_LVD_SUPPORT_RESET_ON_RISING_EDGE      (0UL)             // Voltage monitors support rising edge detections (i.e.
#define BSP_FEATURE_LVD_VBAT_STABILIZATION_TIME_US        (0UL)             // Detection delay time for EXLVDVBAT pin input.
#define BSP_FEATURE_LVD_VERSION                           (1UL)             // Version of the LVD peripheral.
#define BSP_FEATURE_LVD_VRTC_LVL_STABILIZATION_TIME_US    (0UL)             // Stabilization wait time after writing to VRTLVDCR.LVL.
#define BSP_FEATURE_LVD_VRTC_STABILIZATION_TIME_US        (0UL)             // Detection delay time for VRTC pin input.

#define BSP_FEATURE_MACL_SUPPORTED                        (0UL)             // On-chip multiplier and multiply-accumulator is available.

#define BSP_FEATURE_OPAMP_IS_AVAILABLE                    (0UL)
#define BSP_FEATURE_OPAMP_BASE_ADDRESS                    (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_HAS_MIDDLE_SPEED                (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_HAS_SWITCHES                    (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_MIN_WAIT_TIME_HS_US             (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_MIN_WAIT_TIME_LP_US             (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_MIN_WAIT_TIME_MS_US             (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_TRIM_CAPABLE                    (0UL)             // Feature not available on this device.
#define BSP_FEATURE_OPAMP_VARIANT_CHANNEL_MASK            (0x00UL)          // Feature not available on this device.

#define BSP_FEATURE_OSPI_IS_AVAILABLE                     (1UL)
#define BSP_FEATURE_OSPI_DEVICE_0_START_ADDRESS           (0x68000000UL)    // Start address of the CS0 memory mapped region for OSPI.
#define BSP_FEATURE_OSPI_DEVICE_1_START_ADDRESS           (0x70000000UL)    // Start address of the CS1 memory mapped region for OSPI.

#define BSP_FEATURE_OSPI_B_IS_AVAILABLE                   (0UL)
#define BSP_FEATURE_OSPI_B_DEVICE_0_START_ADDRESS         (0x00UL)          // Feature not available on this device.
#define BSP_FEATURE_OSPI_B_DEVICE_1_START_ADDRESS         (0x00UL)          // Feature not available on this device.

#define BSP_FEATURE_POEG_CHANNEL_MASK                     (0x0FUL)          // Mask of valid channels for POEG.
#define BSP_FEATURE_POEG_HAS_POEGG_DERRST                 (0UL)             // Indicates POEGG.DERRSTn registers are available.

#define BSP_FEATURE_QSPI_IS_AVAILABLE                     (1UL)
#define BSP_FEATURE_QSPI_DEVICE_START_ADDRESS             (0x60000000UL)    // Start address of the CS0 memory mapped region for QSPI.

#define BSP_FEATURE_RSIP_AES_B_SUPPORTED                  (0UL)             // The device supports cryptography using AES_B.
#define BSP_FEATURE_RSIP_AES_SUPPORTED                    (0UL)             // The device supports cryptography using AES.
#define BSP_FEATURE_RSIP_RSIP_E11A_SUPPORTED              (0UL)             // The device supports cryptography using RISP_E11A.
#define BSP_FEATURE_RSIP_RSIP_E31A_SUPPORTED              (0UL)             // The device supports cryptography using RISP_E31A.
#define BSP_FEATURE_RSIP_RSIP_E50D_SUPPORTED              (0UL)             // The device supports cryptography using RSIP_E50D.
#define BSP_FEATURE_RSIP_RSIP_E51A_SUPPORTED              (0UL)             // The device supports cryptography using RSIP_E51A.
#define BSP_FEATURE_RSIP_SCE5_SUPPORTED                   (0UL)             // The device supports cryptography using SCE5.
#define BSP_FEATURE_RSIP_SCE5B_SUPPORTED                  (0UL)             // The device supports cryptography using SCE5B.
#define BSP_FEATURE_RSIP_SCE7_SUPPORTED                   (0UL)             // The device supports cryptography using SCE7.
#define BSP_FEATURE_RSIP_SCE9_SUPPORTED                   (1UL)             // The device supports cryptography using SCE9.
#define BSP_FEATURE_RSIP_TRNG_SUPPORTED                   (0UL)             // The device supports a TRNG module.

#define BSP_FEATURE_RTC_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_RTC_HAS_HP_MODE                       (0UL)             // Indicates HP mode is available.
#define BSP_FEATURE_RTC_HAS_RADJ_ADJ6                     (0UL)             // ADJ6 is appended to upper part of RADJ.ADJ[0:5] as ADJ[6].
#define BSP_FEATURE_RTC_HAS_ROPSEL                        (0UL)             // The RCR4.ROPSEL field is available.
#define BSP_FEATURE_RTC_HAS_TCEN                          (1UL)             // Timer capture is available.
#define BSP_FEATURE_RTC_IS_IRTC                           (0UL)             // RTC has a separate power domain (VRTC) for the sub-clock oscillator and RTC peripheral.
#define BSP_FEATURE_RTC_RTCCR_CHANNELS                    (3UL)             // Number of RTCCRn registers that are available.

#define BSP_FEATURE_SAU_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_SAU_UART_VALID_CHANNEL_MASK           (0x00UL)          // Feature not available on this device.

#define BSP_FEATURE_SCI_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_SCI_ADDRESS_MATCH_CHANNELS            (0x03F9UL)        // Mask of channels with data compare match (DCCR) available.
#define BSP_FEATURE_SCI_CHANNELS                          (0x03FFUL)        // Mask of available SCI channels.
#define BSP_FEATURE_SCI_CLOCK                             (FSP_PRIV_CLOCK_PCLKA)    // Clock source routed to the SCI peripherals.
#define BSP_FEATURE_SCI_IRDA_CHANNEL_MASK                 (0x00UL)          // Mask of channels that support IrDA.
#define BSP_FEATURE_SCI_IRDA_SUPPORTED                    (0UL)             // Indicates IrDA is supported on at least one SCI channel.
#define BSP_FEATURE_SCI_LIN_CHANNELS                      (0x06UL)          // Mask of channels that can support LIN.
#define BSP_FEATURE_SCI_SPI_SCKSEL_VALUE                  (0UL)             // Mask indicating CCR4.SCKSEL is available.
#define BSP_FEATURE_SCI_UART_ABCSE_RESTRICTED_CHANNELS    (0x06UL)          // List of channels that do not support ABCSE functionality.
#define BSP_FEATURE_SCI_UART_CSTPEN_CHANNELS              (0x03F9UL)        // Mask of channels which support CTS external pins.
#define BSP_FEATURE_SCI_UART_DE_IS_INVERTED               (0UL)             // Indicates the PSEL value used to enable `DEn` output signal is opposite compared to other MCUs.
#define BSP_FEATURE_SCI_UART_FIFO_CHANNELS                (0x03F9UL)        // Mask of channels which support the UART FIFO.
#define BSP_FEATURE_SCI_UART_FIFO_DEPTH                   (16UL)            // Depth of the UART FIFO if available.
#define BSP_FEATURE_SCI_VERSION                           (1UL)             // Version of the SCI peripheral.

#define BSP_FEATURE_SDHI_IS_AVAILABLE                     (1UL)
#define BSP_FEATURE_SDHI_CLOCK                            (FSP_PRIV_CLOCK_PCLKB)    // Clock source for the SDHI peripheral clock.
#define BSP_FEATURE_SDHI_HAS_CARD_DETECTION               (1UL)             // Peripheral can detect if a card is present or not based on signal pull-ups.
#define BSP_FEATURE_SDHI_MIN_CLOCK_DIVISION_SHIFT         (0UL)             // Smallest shift value for the divider pre-scaller available on the SDHI clock.
#define BSP_FEATURE_SDHI_SUPPORTS_8_BIT_MMC               (1UL)             // Supports 8-bit data bus width to the MMC device.
#define BSP_FEATURE_SDHI_VALID_CHANNEL_MASK               (0x01UL)          // Mask of valid SDHI channels.

#define BSP_FEATURE_SDRAM_START_ADDRESS                   (0x00UL)          // Start address of the external address space for SDRAM memory.

#define BSP_FEATURE_SLCDC_IS_AVAILABLE                    (0UL)
#define BSP_FEATURE_SLCDC_CONTRAST_MAX                    (0UL)             // Feature not available on this device.
#define BSP_FEATURE_SLCDC_CONTRAST_MAX_4BIAS              (0UL)             // Feature not available on this device.
#define BSP_FEATURE_SLCDC_HAS_8_TIME_SLICE                (0UL)             // Feature not available on this device.
#define BSP_FEATURE_SLCDC_HAS_INTERNAL_VOLT_GEN           (0UL)             // Feature not available on this device.
#define BSP_FEATURE_SLCDC_HAS_VL1SEL                      (0UL)             // Feature not available on this device.
#define BSP_FEATURE_SLCDC_HAS_VLCD_MDSET2                 (0UL)             // Feature not available on this device.
#define BSP_FEATURE_SLCDC_MAX_NUM_SEG                     (0UL)             // Feature not available on this device.

#define BSP_FEATURE_SPI_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_SPI_CLK                               (FSP_PRIV_CLOCK_PCLKA)    // Clock source for SPI peripherals.
#define BSP_FEATURE_SPI_HAS_SPCR3                         (1UL)             // SPCR3 register is available.
#define BSP_FEATURE_SPI_HAS_SSL_LEVEL_KEEP                (1UL)             // SPCMDn.SSLKP field is available.
#define BSP_FEATURE_SPI_MAX_CHANNEL                       (2UL)             // Number of available SPI channels.
#define BSP_FEATURE_SPI_SSL_LEVEL_KEEP_VALID_CHANNEL_MASK (0x03UL)          // Mask of channel indices that support SSL Level Keep.

#define BSP_FEATURE_SRAM_SRAMWTSC_WAIT_CYCLE_ENABLE       (0x01UL)          // Mask of bits needed to enable SRAM wait for all regions.

#define BSP_FEATURE_SSI_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_SSI_FIFO_NUM_STAGES                   (32UL)            // Depth of the SSI data FIFO.
#define BSP_FEATURE_SSI_VALID_CHANNEL_MASK                (1UL)             // Mask of valid SSI channel indices.

#define BSP_FEATURE_SYSC_HAS_VBTICTLR                     (1UL)             // System supports VBATT input control to the RTC.

#define BSP_FEATURE_TAU_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_TAU_VALID_CHANNEL_MASK                (0x00UL)          // Feature not available on this device.

#define BSP_FEATURE_TFU_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_TFU_SUPPORTED                         (0UL)             // Feature not available on this device.

#define BSP_FEATURE_TML_IS_AVAILABLE                      (0UL)
#define BSP_FEATURE_TML_MAX_CLOCK_DIVIDER                 (0UL)             // Feature not available on this device.
#define BSP_FEATURE_TML_NUM_CHANNELS                      (0UL)             // Feature not available on this device.
#define BSP_FEATURE_TML_VALID_CHANNEL_MASK                (0x00UL)          // Feature not available on this device.

#define BSP_FEATURE_TRNG_HAS_MODULE_STOP                  (0UL)             // A module stop control is available for TRNG.

#define BSP_FEATURE_TZ_IS_AVAILABLE                       (1UL)
#define BSP_FEATURE_TZ_HAS_DLM                            (1UL)             // Device Lifecycle Management Monitor (DLMMON) register is available.
#define BSP_FEATURE_TZ_HAS_TRUSTZONE                      (1UL)             // The device supports Arm TrustZone.
#define BSP_FEATURE_TZ_NS_OFFSET                          (0x00UL)          // Offset for the Non-secure address space of a peripheral.
#define BSP_FEATURE_TZ_VERSION                            (1UL)             // Version of the TrustZone implementation.

#define BSP_FEATURE_UARTA_IS_AVAILABLE                    (0UL)
#define BSP_FEATURE_UARTA_HAS_CLOCK_OUTPUT                (0UL)             // Feature not available on this device.
#define BSP_FEATURE_UARTA_MSTP_OFFSET                     (0UL)             // Feature not available on this device.
#define BSP_FEATURE_UARTA_PCLK_RESTRICTION                (0UL)             // Feature not available on this device.

#define BSP_FEATURE_ULPT_IS_AVAILABLE                     (0UL)
#define BSP_FEATURE_ULPT_MAX_CHANNEL_NUM                  (0UL)             // Feature not available on this device.
#define BSP_FEATURE_ULPT_VALID_CHANNEL_MASK               (0UL)             // Feature not available on this device.

#define BSP_FEATURE_USB_IS_AVAILABLE                      (1UL)
#define BSP_FEATURE_USB_HAS_NOT_HOST                      (0UL)             // Indicates that USB Host mode is not available.
#define BSP_FEATURE_USB_HAS_PIPE04567                     (0UL)             // USB peripheral only has pipes 0, 4, 5, 6, and 7.
#define BSP_FEATURE_USB_HAS_TYPEC                         (0UL)             // Supports USB-C control specifications.
#define BSP_FEATURE_USB_HAS_USBFS                         (1UL)             // Supports USB 2.0 Full-Speed mode.
#define BSP_FEATURE_USB_HAS_USBFS_BC                      (1UL)             // Supports battery charging in full-speed mode.
#define BSP_FEATURE_USB_HAS_USBHS                         (1UL)             // Supports USB 2.0 High-Speed mode.
#define BSP_FEATURE_USB_HAS_USBHS_BC                      (1UL)             // Supports battery charging in high-speed mode.
#define BSP_FEATURE_USB_HAS_USBLS_PERI                    (0UL)             // Supports low-speed connections in device controller mode.
#define BSP_FEATURE_USB_REG_PHYSECTRL_CNEN                (1UL)             // Indicates the PHYSECTRL.CNEN field is available.
#define BSP_FEATURE_USB_REG_PHYSLEW                       (0UL)             // Indicates the PHYSLEW register is available.
#define BSP_FEATURE_USB_REG_PHYSLEW_VALUE                 (0x00UL)          // Reset value of the PHYSLEW register.
#define BSP_FEATURE_USB_REG_UCKSEL_UCKSELC                (0UL)             // Indicates the UCKSEL.UCKSELC bit field is available.
#define BSP_FEATURE_USB_REG_USBMC_VDCEN                   (0UL)             // Indicates the USBMC.VDCEN bit field is available.
#define BSP_FEATURE_USB_REG_USBMC_VDDUSBE                 (0UL)             // Indicates the USBMC.VDDUSBE bit field is available.

// *UNCRUSTIFY-ON*

#endif
