#include "key_app.h"

uint8_t key_val,key_down,key_up,key_old;
bool sw2_long_flag = false;
bool sw3_long_flag = false;
uint16_t sw2_time = 0;
uint16_t sw3_time = 0;

void key_init(void)
{
    sw2_long_flag = sw3_long_flag = false;
    sw2_time = sw3_time = 0;
}

uint8_t key_read(void)
{
    bsp_io_level_t state;
    uint8_t temp = 0;
    R_IOPORT_PinRead(&g_ioport_ctrl, KEY1_SW2_PIN, &state);
    if(state == BSP_IO_LEVEL_LOW ) temp = 1; // sw2按下
    R_IOPORT_PinRead(&g_ioport_ctrl, KEY2_SW3_PIN, &state);
    if(state == BSP_IO_LEVEL_LOW ) temp = 2; // sw3按下
    return temp;
}

/* KEY 外部中断初始化函数 */
/*void Key_IRQ_Init(void)
{
    Open ICU module
   R_ICU_ExternalIrqOpen(&g_external_irq9_ctrl, &g_external_irq9_cfg);
   R_ICU_ExternalIrqOpen(&g_external_irq10_ctrl, &g_external_irq10_cfg);
    允许中断
   R_ICU_ExternalIrqEnable(&g_external_irq9_ctrl);
   R_ICU_ExternalIrqEnable(&g_external_irq10_ctrl);
}*/

/*
// 按键按下标志
volatile bool key1_sw2_press = false;
volatile bool key2_sw3_press = false;
// 按键长按标志
volatile bool key1_sw2_press_long = false;
volatile bool key2_sw3_press_long = false;
*/

/* 按键中断回调函数 */
/*void key_callback(external_irq_callback_args_t *p_args)
{
    判断中断通道
   if (9 == p_args->channel)
   {
      key1_sw2_press = true;   // 按键KEY1_SW2按下
   }
   else if (10 == p_args->channel)
   {
      key2_sw3_press = true;   // 按键KEY2_SW3按下
   }
}*/

/*
void key_task(void)
{
    if(key1_sw2_press)   //扫描按键1
    {
        key1_sw2_press = false;
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_00, BSP_IO_LEVEL_LOW); //LED1亮
    }
    if(key2_sw3_press)   //扫描按键2
    {
        key2_sw3_press = false;
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_00, BSP_IO_LEVEL_HIGH); //LED1灭
    }
}
*/
