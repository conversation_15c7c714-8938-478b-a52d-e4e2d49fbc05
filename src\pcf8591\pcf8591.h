#ifndef PCF8591_PCF8591_H_
#define PCF8591_PCF8591_H_

#include "mydefine.h"
#include "my_iic.h"

/* PCF8591相关定义 */
#define PCF8591_ADDR        0x48    // PCF8591默认地址

/* PCF8591控制字节定义 */
#define PCF8591_AIN0        0x00    // 模拟输入通道0
#define PCF8591_AIN1        0x01    // 模拟输入通道1
#define PCF8591_AIN2        0x02    // 模拟输入通道2
#define PCF8591_AIN3        0x03    // 模拟输入通道3
#define PCF8591_AOUT_EN     0x40    // 使能模拟输出

/* PCF8591函数声明 */
void pcf8591_init(void);
uint8_t pcf8591_read_adc(uint8_t channel);
void pcf8591_write_dac(uint8_t value);

#endif /* PCF8591_PCF8591_H_ */
