<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="10">
  <generalSettings>
    <option key="#Board#" value="board.custom"/>
    <option key="CPU" value="RA6M5"/>
    <option key="Core" value="CM33"/>
    <option key="#TargetName#" value="R7FA6M5BH3CFC"/>
    <option key="#TargetARCHITECTURE#" value="cortex-m33"/>
    <option key="#DeviceCommand#" value="R7FA6M5BH"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R7FA6M5BH3CFC.pincfg"/>
    <option key="#FSPVersion#" value="5.8.0"/>
    <option key="#SELECTED_TOOLCHAIN#" value="gcc-arm-embedded"/>
    <option key="#ToolchainVersion#" value="10.3.1.20210824"/>
  </generalSettings>
  <raBspConfiguration>
    <config id="config.bsp.ra6m5.R7FA6M5BH3CFC">
      <property id="config.bsp.part_number" value="config.bsp.part_number.value"/>
      <property id="config.bsp.rom_size_bytes" value="config.bsp.rom_size_bytes.value"/>
      <property id="config.bsp.rom_size_bytes_hidden" value="2097152"/>
      <property id="config.bsp.ram_size_bytes" value="config.bsp.ram_size_bytes.value"/>
      <property id="config.bsp.data_flash_size_bytes" value="config.bsp.data_flash_size_bytes.value"/>
      <property id="config.bsp.package_style" value="config.bsp.package_style.value"/>
      <property id="config.bsp.package_pins" value="config.bsp.package_pins.value"/>
      <property id="config.bsp.irq_count_hidden" value="96"/>
    </config>
    <config id="config.bsp.ra6m5">
      <property id="config.bsp.series" value="config.bsp.series.value"/>
    </config>
    <config id="config.bsp.ra6m5.fsp">
      <property id="config.bsp.fsp.inline_irq_functions" value="config.bsp.common.inline_irq_functions.enabled"/>
      <property id="config.bsp.fsp.tz.exception_response" value="config.bsp.fsp.tz.exception_response.nmi"/>
      <property id="config.bsp.fsp.tz.cmsis.bfhfnmins" value="config.bsp.fsp.tz.cmsis.bfhfnmins.secure"/>
      <property id="config.bsp.fsp.tz.cmsis.sysresetreqs" value="config.bsp.fsp.tz.cmsis.sysresetreqs.secure_only"/>
      <property id="config.bsp.fsp.tz.cmsis.s_priority_boost" value="config.bsp.fsp.tz.cmsis.s_priority_boost.disabled"/>
      <property id="config.bsp.fsp.tz.csar" value="config.bsp.fsp.tz.csar.both"/>
      <property id="config.bsp.fsp.tz.rstsar" value="config.bsp.fsp.tz.rstsar.both"/>
      <property id="config.bsp.fsp.tz.bbfsar" value="config.bsp.fsp.tz.bbfsar.both"/>
      <property id="config.bsp.fsp.tz.sramsar.sramprcr" value="config.bsp.fsp.tz.sramsar.sramprcr.both"/>
      <property id="config.bsp.fsp.tz.sramsar.sramecc" value="config.bsp.fsp.tz.sramsar.sramecc.both"/>
      <property id="config.bsp.fsp.tz.stbramsar" value="config.bsp.fsp.tz.stbramsar.both"/>
      <property id="config.bsp.fsp.tz.bussara" value="config.bsp.fsp.tz.bussara.both"/>
      <property id="config.bsp.fsp.tz.bussarb" value="config.bsp.fsp.tz.bussarb.both"/>
      <property id="config.bsp.fsp.tz.banksel_sel" value="config.bsp.fsp.tz.banksel_sel.both"/>
      <property id="config.bsp.fsp.tz.uninitialized_ns_application_fallback" value="config.bsp.fsp.tz.uninitialized_ns_application_fallback.enabled"/>
      <property id="config.bsp.fsp.cache_line_size" value="config.bsp.fsp.cache_line_size.32"/>
      <property id="config.bsp.fsp.OFS0.iwdt_start_mode" value="config.bsp.fsp.OFS0.iwdt_start_mode.disabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_timeout" value="config.bsp.fsp.OFS0.iwdt_timeout.2048"/>
      <property id="config.bsp.fsp.OFS0.iwdt_divisor" value="config.bsp.fsp.OFS0.iwdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_end" value="config.bsp.fsp.OFS0.iwdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_start" value="config.bsp.fsp.OFS0.iwdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.iwdt_reset_interrupt" value="config.bsp.fsp.OFS0.iwdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.iwdt_stop_control" value="config.bsp.fsp.OFS0.iwdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS0.wdt_start_mode" value="config.bsp.fsp.OFS0.wdt_start_mode.register"/>
      <property id="config.bsp.fsp.OFS0.wdt_timeout" value="config.bsp.fsp.OFS0.wdt_timeout.16384"/>
      <property id="config.bsp.fsp.OFS0.wdt_divisor" value="config.bsp.fsp.OFS0.wdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_end" value="config.bsp.fsp.OFS0.wdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_start" value="config.bsp.fsp.OFS0.wdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.wdt_reset_interrupt" value="config.bsp.fsp.OFS0.wdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.wdt_stop_control" value="config.bsp.fsp.OFS0.wdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS1_SEL.voltage_detection0_level" value="config.bsp.fsp.OFS1_SEL.voltage_detection0_level.secure"/>
      <property id="config.bsp.fsp.OFS1_SEL.voltage_detection0.start" value="config.bsp.fsp.OFS1_SEL.voltage_detection0.start.secure"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0.start" value="config.bsp.fsp.OFS1.voltage_detection0.start.disabled"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0_level" value="config.bsp.fsp.OFS1.voltage_detection0_level.280"/>
      <property id="config.bsp.fsp.OFS1.hoco_osc" value="config.bsp.fsp.OFS1.hoco_osc.disabled"/>
      <property id="config.bsp.fsp.BPS.BPS0" value=""/>
      <property id="config.bsp.fsp.BPS.BPS1" value=""/>
      <property id="config.bsp.fsp.BPS.BPS2" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS0" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS1" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS2" value=""/>
      <property id="config.bsp.fsp.dual_bank" value="config.bsp.fsp.dual_bank.disabled"/>
      <property id="config.bsp.fsp.hoco_fll" value="config.bsp.fsp.hoco_fll.disabled"/>
      <property id="config.bsp.common.main_osc_wait" value="config.bsp.common.main_osc_wait.wait_8163"/>
      <property id="config.bsp.fsp.mcu.adc.max_freq_hz" value="********"/>
      <property id="config.bsp.fsp.mcu.sci_uart.max_baud" value="********"/>
      <property id="config.bsp.fsp.mcu.adc.sample_and_hold" value="0"/>
      <property id="config.bsp.fsp.mcu.adc.sensors_are_exclusive" value="0"/>
      <property id="config.bsp.fsp.mcu.sci_spi.max_bitrate" value="********"/>
      <property id="config.bsp.fsp.mcu.spi.max_bitrate" value="********"/>
      <property id="config.bsp.fsp.mcu.iic_master.rate.rate_fastplus" value="1"/>
      <property id="config.bsp.fsp.mcu.iic_master.fastplus_channels" value="0x3"/>
      <property id="config.bsp.fsp.mcu.iic_slave.rate.rate_fastplus" value="1"/>
      <property id="config.bsp.fsp.mcu.iic_slave.fastplus_channels" value="0x3"/>
      <property id="config.bsp.fsp.mcu.canfd.num_channels" value="2"/>
      <property id="config.bsp.fsp.mcu.canfd.rx_fifos" value="8"/>
      <property id="config.bsp.fsp.mcu.canfd.buffer_ram" value="4864"/>
      <property id="config.bsp.fsp.mcu.canfd.afl_rules" value="128"/>
      <property id="config.bsp.fsp.mcu.canfd.afl_rules_each_chnl" value="64"/>
      <property id="config.bsp.fsp.mcu.canfd.max_data_rate_hz" value="8"/>
      <property id="config.bsp.fsp.mcu.sci_uart.cstpen_channels" value="0x03F9"/>
      <property id="config.bsp.fsp.mcu.gpt.pin_count_source_channels" value="0xFFFF"/>
      <property id="config.bsp.fsp.mcu.adc_dmac.samples_per_channel" value="65535"/>
    </config>
    <config id="config.bsp.ra">
      <property id="config.bsp.common.main" value="0x800"/>
      <property id="config.bsp.common.heap" value="0x1000"/>
      <property id="config.bsp.common.vcc" value="3300"/>
      <property id="config.bsp.common.checking" value="config.bsp.common.checking.disabled"/>
      <property id="config.bsp.common.assert" value="config.bsp.common.assert.none"/>
      <property id="config.bsp.common.soft_reset" value="config.bsp.common.soft_reset.disabled"/>
      <property id="config.bsp.common.main_osc_populated" value="config.bsp.common.main_osc_populated.enabled"/>
      <property id="config.bsp.common.pfs_protect" value="config.bsp.common.pfs_protect.enabled"/>
      <property id="config.bsp.common.c_runtime_init" value="config.bsp.common.c_runtime_init.enabled"/>
      <property id="config.bsp.common.early_init" value="config.bsp.common.early_init.disabled"/>
      <property id="config.bsp.common.main_osc_clock_source" value="config.bsp.common.main_osc_clock_source.crystal"/>
      <property id="config.bsp.common.subclock_populated" value="config.bsp.common.subclock_populated.enabled"/>
      <property id="config.bsp.common.subclock_drive" value="config.bsp.common.subclock_drive.standard"/>
      <property id="config.bsp.common.subclock_stabilization_ms" value="1000"/>
    </config>
  </raBspConfiguration>
  <raClockConfiguration>
    <node id="board.clock.xtal.freq" mul="24000000" option="_edit"/>
    <node id="board.clock.hoco.freq" option="board.clock.hoco.freq.20m"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.32768"/>
    <node id="board.clock.moco.freq" option="board.clock.moco.freq.8m"/>
    <node id="board.clock.subclk.freq" option="board.clock.subclk.freq.32768"/>
    <node id="board.clock.pll.source" option="board.clock.pll.source.xtal"/>
    <node id="board.clock.pll.div" option="board.clock.pll.div.3"/>
    <node id="board.clock.pll.mul" option="board.clock.pll.mul.250"/>
    <node id="board.clock.pll.display" option="board.clock.pll.display.value"/>
    <node id="board.clock.pll2.source" option="board.clock.pll2.source.disabled"/>
    <node id="board.clock.pll2.div" option="board.clock.pll2.div.2"/>
    <node id="board.clock.pll2.mul" option="board.clock.pll2.mul.200"/>
    <node id="board.clock.pll2.display" option="board.clock.pll2.display.value"/>
    <node id="board.clock.clock.source" option="board.clock.clock.source.pll"/>
    <node id="board.clock.clkout.source" option="board.clock.clkout.source.disabled"/>
    <node id="board.clock.uclk.source" option="board.clock.uclk.source.disabled"/>
    <node id="board.clock.u60ck.source" option="board.clock.u60ck.source.disabled"/>
    <node id="board.clock.octaspiclk.source" option="board.clock.octaspiclk.source.disabled"/>
    <node id="board.clock.canfdclk.source" option="board.clock.canfdclk.source.disabled"/>
    <node id="board.clock.cecclk.source" option="board.clock.cecclk.source.disabled"/>
    <node id="board.clock.iclk.div" option="board.clock.iclk.div.1"/>
    <node id="board.clock.pclka.div" option="board.clock.pclka.div.2"/>
    <node id="board.clock.pclkb.div" option="board.clock.pclkb.div.4"/>
    <node id="board.clock.pclkc.div" option="board.clock.pclkc.div.4"/>
    <node id="board.clock.pclkd.div" option="board.clock.pclkd.div.2"/>
    <node id="board.clock.bclk.div" option="board.clock.bclk.div.2"/>
    <node id="board.clock.bclkout.div" option="board.clock.bclkout.div.2"/>
    <node id="board.clock.fclk.div" option="board.clock.fclk.div.4"/>
    <node id="board.clock.clkout.div" option="board.clock.clkout.div.1"/>
    <node id="board.clock.uclk.div" option="board.clock.uclk.div.5"/>
    <node id="board.clock.u60ck.div" option="board.clock.u60ck.div.1"/>
    <node id="board.clock.octaspiclk.div" option="board.clock.octaspiclk.div.1"/>
    <node id="board.clock.canfdclk.div" option="board.clock.canfdclk.div.6"/>
    <node id="board.clock.cecclk.div" option="board.clock.cecclk.div.1"/>
    <node id="board.clock.iclk.display" option="board.clock.iclk.display.value"/>
    <node id="board.clock.pclka.display" option="board.clock.pclka.display.value"/>
    <node id="board.clock.pclkb.display" option="board.clock.pclkb.display.value"/>
    <node id="board.clock.pclkc.display" option="board.clock.pclkc.display.value"/>
    <node id="board.clock.pclkd.display" option="board.clock.pclkd.display.value"/>
    <node id="board.clock.bclk.display" option="board.clock.bclk.display.value"/>
    <node id="board.clock.bclkout.display" option="board.clock.bclkout.display.value"/>
    <node id="board.clock.fclk.display" option="board.clock.fclk.display.value"/>
    <node id="board.clock.clkout.display" option="board.clock.clkout.display.value"/>
    <node id="board.clock.uclk.display" option="board.clock.uclk.display.value"/>
    <node id="board.clock.u60ck.display" option="board.clock.u60ck.display.value"/>
    <node id="board.clock.octaspiclk.display" option="board.clock.octaspiclk.display.value"/>
    <node id="board.clock.canfdclk.display" option="board.clock.canfdclk.display.value"/>
    <node id="board.clock.cecclk.display" option="board.clock.cecclk.display.value"/>
  </raClockConfiguration>
  <raComponentSelection>
    <component apiversion="" class="Common" condition="" group="all" subgroup="fsp_common" variant="" vendor="Renesas" version="5.8.0">
      <description>Board Support Package Common Files</description>
      <originalPack>Renesas.RA.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_ioport" variant="" vendor="Renesas" version="5.8.0">
      <description>I/O Port</description>
      <originalPack>Renesas.RA.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="CMSIS" condition="" group="CMSIS5" subgroup="CoreM" variant="" vendor="Arm" version="6.1.0+fsp.5.8.0">
      <description>Arm CMSIS Version 6 - Core (M)</description>
      <originalPack>Arm.CMSIS6.6.1.0+fsp.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="Board" subgroup="custom" variant="" vendor="Renesas" version="5.8.0">
      <description>Custom Board Support Files</description>
      <originalPack>Renesas.RA_board_custom.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m5" subgroup="device" variant="R7FA6M5BH3CFC" vendor="Renesas" version="5.8.0">
      <description>Board support package for R7FA6M5BH3CFC</description>
      <originalPack>Renesas.RA_mcu_ra6m5.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m5" subgroup="device" variant="" vendor="Renesas" version="5.8.0">
      <description>Board support package for RA6M5</description>
      <originalPack>Renesas.RA_mcu_ra6m5.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m5" subgroup="fsp" variant="" vendor="Renesas" version="5.8.0">
      <description>Board support package for RA6M5 - FSP Data</description>
      <originalPack>Renesas.RA_mcu_ra6m5.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m5" subgroup="events" variant="" vendor="Renesas" version="5.8.0">
      <description>Board support package for RA6M5 - Events</description>
      <originalPack>Renesas.RA_mcu_ra6m5.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_adc" variant="" vendor="Renesas" version="5.8.0">
      <description>A/D Converter</description>
      <originalPack>Renesas.RA.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_gpt" variant="" vendor="Renesas" version="5.8.0">
      <description>General PWM Timer</description>
      <originalPack>Renesas.RA.5.8.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_sci_uart" variant="" vendor="Renesas" version="5.8.0">
      <description>SCI UART</description>
      <originalPack>Renesas.RA.5.8.0.pack</originalPack>
    </component>
  </raComponentSelection>
  <raElcConfiguration/>
  <raIcuConfiguration/>
  <raModuleConfiguration>
    <module id="module.driver.ioport_on_ioport.0">
      <property id="module.driver.ioport.name" value="g_ioport"/>
      <property id="module.driver.ioport.elc_trigger_ioport1" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport2" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport3" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport4" value="_disabled"/>
      <property id="module.driver.ioport.pincfg" value="g_bsp_pin_cfg"/>
    </module>
    <module id="module.driver.uart_on_sci_uart.536471316">
      <property id="module.driver.uart.name" value="g_uart4"/>
      <property id="module.driver.uart.channel" value="4"/>
      <property id="module.driver.uart.data_bits" value="module.driver.uart.data_bits.data_bits_8"/>
      <property id="module.driver.uart.parity" value="module.driver.uart.parity.parity_off"/>
      <property id="module.driver.uart.stop_bits" value="module.driver.uart.stop_bits.stop_bits_1"/>
      <property id="module.driver.uart.baud" value="115200"/>
      <property id="module.driver.uart.baudrate_modulation" value="module.driver.uart.baudrate_modulation.disabled"/>
      <property id="module.driver.uart.baudrate_max_err" value="5"/>
      <property id="module.driver.uart.flow_control" value="module.driver.uart.flow_control.rts"/>
      <property id="module.driver.uart.pin_control_port" value="module.driver.uart.pin_control_port.PORT_DISABLE"/>
      <property id="module.driver.uart.pin_control_pin" value="module.driver.uart.pin_control_pin.PIN_DISABLE"/>
      <property id="module.driver.uart.clk_src" value="module.driver.uart.clk_src.int_clk"/>
      <property id="module.driver.uart.rx_edge_start" value="module.driver.uart.rx_edge_start.falling_edge"/>
      <property id="module.driver.uart.noisecancel_en" value="module.driver.uart.noisecancel_en.disabled"/>
      <property id="module.driver.uart.rx_fifo_trigger" value="module.driver.uart.rx_fifo_trigger.max"/>
      <property id="module.driver.uart.irda.ire" value="module.driver.uart.irda.ire.disabled"/>
      <property id="module.driver.uart.irda.irrxinv" value="module.driver.uart.irda.irrxinv.disabled"/>
      <property id="module.driver.uart.irda.irtxinv" value="module.driver.uart.irda.irtxinv.disabled"/>
      <property id="module.driver.uart.rs485.de_enable" value="module.driver.uart.rs485.de_enable.disabled"/>
      <property id="module.driver.uart.rs485.de_polarity" value="module.driver.uart.rs485.de_polarity.high"/>
      <property id="module.driver.uart.rs485.de_port_number" value="module.driver.uart.rs485.de_port_number.PORT_DISABLE"/>
      <property id="module.driver.uart.rs485.de_pin_number" value="module.driver.uart.rs485.de_pin_number.PIN_DISABLE"/>
      <property id="module.driver.uart.callback" value="debug_uart4_callback"/>
      <property id="module.driver.uart.rxi_ipl" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.uart.txi_ipl" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.uart.tei_ipl" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.uart.eri_ipl" value="board.icu.common.irq.priority12"/>
    </module>
    <module id="module.driver.adc_on_adc.123925952">
      <property id="module.driver.adc.name" value="g_adc0"/>
      <property id="module.driver.adc.unit" value="0"/>
      <property id="module.driver.adc.resolution" value="module.driver.adc.resolution.resolution_12_bit"/>
      <property id="module.driver.adc.alignment" value="module.driver.adc.alignment.alignment_right"/>
      <property id="module.driver.adc.clearing" value="module.driver.adc.clearing.clear_after_read_on"/>
      <property id="module.driver.adc.mode" value="module.driver.adc.mode.mode_single_scan"/>
      <property id="module.driver.adc.mode.dt" value="module.driver.adc.mode.dt.disabled"/>
      <property id="module.driver.adc.scan_mask" value="module.driver.adc.scan_mask.channel_0"/>
      <property id="module.driver.adc.scan_mask_group_b" value=""/>
      <property id="module.driver.adc.trigger" value="enum.driver.adc.trigger.trigger_software"/>
      <property id="module.driver.adc.trigger_group_b" value="_disabled"/>
      <property id="module.driver.adc.priority_group_a" value="module.driver.adc.priority_group_a.group_a_priority_off"/>
      <property id="module.driver.adc.add_average_count" value="module.driver.adc.add_average_count.add_off"/>
      <property id="module.driver.adc.adc_vref_control" value="module.driver.adc.adc_vref_control.vrefh"/>
      <property id="module.driver.adc.add_mask" value=""/>
      <property id="module.driver.adc.sample_hold_mask" value=""/>
      <property id="module.driver.adc.sample_hold_states" value="24"/>
      <property id="module.driver.adc.compare.window_mode" value="module.driver.adc.compare.window_mode.disabled"/>
      <property id="module.driver.adc.compare.event_mode" value="module.driver.adc.compare.event_mode.or"/>
      <property id="module.driver.adc.compare.window_a.enable" value="module.driver.adc.compare.window_a.enable.disabled"/>
      <property id="module.driver.adc.compare.window_a.channels" value=""/>
      <property id="module.driver.adc.compare.window_a.channel_mode" value=""/>
      <property id="module.driver.adc.compare.window_a.ref_lower" value="0"/>
      <property id="module.driver.adc.compare.window_a.ref_upper" value="0"/>
      <property id="module.driver.adc.compare.window_b.enable" value="module.driver.adc.compare.window_b.enable.disabled"/>
      <property id="module.driver.adc.compare.window_b.channel" value="module.driver.adc.compare.window_b.channel.channel_0"/>
      <property id="module.driver.adc.compare.window_b.mode" value="module.driver.adc.compare.window_b.mode"/>
      <property id="module.driver.adc.compare.window_b.ref_lower" value="0"/>
      <property id="module.driver.adc.compare.window_b.ref_upper" value="0"/>
      <property id="module.driver.adc.p_callback" value="adc_callback"/>
      <property id="module.driver.adc.scan_end_ipl" value="board.icu.common.irq.priority2"/>
      <property id="module.driver.adc.scan_end_b_ipl" value="_disabled"/>
      <property id="module.driver.adc.window_a_ipl" value="_disabled"/>
      <property id="module.driver.adc.window_b_ipl" value="_disabled"/>
      <property id="module.driver.adc.adbuf" value="module.driver.adc.adbuf.disabled"/>
    </module>
    <module id="module.driver.timer_on_gpt.379762189">
      <property id="module.driver.timer.name" value="g_timer0"/>
      <property id="module.driver.timer.channel" value="0"/>
      <property id="module.driver.timer.mode" value="module.driver.timer.mode.mode_periodic"/>
      <property id="module.driver.timer.period" value="1"/>
      <property id="module.driver.timer.compare_match.a.status" value="module.driver.timer.compare_match.a.status.disabled"/>
      <property id="module.driver.timer.compare_match.a.value" value="0"/>
      <property id="module.driver.timer.compare_match.b.status" value="module.driver.timer.compare_match.b.status.disabled"/>
      <property id="module.driver.timer.compare_match.b.value" value="0"/>
      <property id="module.driver.timer.unit" value="module.driver.timer.unit.unit_period_sec"/>
      <property id="module.driver.timer.gtior.gtioa.initial_output_level" value="module.driver.timer.gtior.gtioa.initial_output_level.low"/>
      <property id="module.driver.timer.gtior.gtioa.cycle_end_output_level" value="module.driver.timer.gtior.gtioa.cycle_end_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtioa.compare_match_output_level" value="module.driver.timer.gtior.gtioa.compare_match_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtioa.count_stop_retain" value="module.driver.timer.gtior.gtioa.count_stop_retain.disabled"/>
      <property id="module.driver.timer.gtior.gtiob.initial_output_level" value="module.driver.timer.gtior.gtiob.initial_output_level.low"/>
      <property id="module.driver.timer.gtior.gtiob.cycle_end_output_level" value="module.driver.timer.gtior.gtiob.cycle_end_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtiob.compare_match_output_level" value="module.driver.timer.gtior.gtiob.compare_match_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtiob.count_stop_retain" value="module.driver.timer.gtior.gtiob.count_stop_retain.disabled"/>
      <property id="module.driver.timer.gtior.custom_waveform_enable" value="module.driver.timer.gtior.custom_waveform_enable.disabled"/>
      <property id="module.driver.timer.duty_cycle" value="50"/>
      <property id="module.driver.timer.gtioca_output_enabled" value="module.driver.timer.gtioca_output_enabled.false"/>
      <property id="module.driver.timer.gtioca_stop_level" value="module.driver.timer.gtioca_stop_level.pin_level_low"/>
      <property id="module.driver.timer.gtiocb_output_enabled" value="module.driver.timer.gtiocb_output_enabled.false"/>
      <property id="module.driver.timer.gtiocb_stop_level" value="module.driver.timer.gtiocb_stop_level.pin_level_low"/>
      <property id="module.driver.timer.count_up_source" value=""/>
      <property id="module.driver.timer.count_down_source" value=""/>
      <property id="module.driver.timer.start_source" value=""/>
      <property id="module.driver.timer.stop_source" value=""/>
      <property id="module.driver.timer.clear_source" value=""/>
      <property id="module.driver.timer.capture_a_source" value=""/>
      <property id="module.driver.timer.capture_b_source" value=""/>
      <property id="module.driver.timer.gtioca_filter" value="module.driver.timer.gtioc_filter.gtioc_filter_none"/>
      <property id="module.driver.timer.gtiocb_filter" value="module.driver.timer.gtioc_filter.gtioc_filter_none"/>
      <property id="module.driver.timer.p_callback" value="gpt0_timing_callback"/>
      <property id="module.driver.timer.ipl" value="board.icu.common.irq.priority10"/>
      <property id="module.driver.timer.capture_a_ipl" value="_disabled"/>
      <property id="module.driver.timer.capture_b_ipl" value="_disabled"/>
      <property id="module.driver.timer.trough_ipl" value="_disabled"/>
      <property id="module.driver.timer.extra" value="module.driver.timer.extra.disabled"/>
      <property id="module.driver.timer.poeg_link" value="enum.driver.poeg.channels.poeg_link_poeg0"/>
      <property id="module.driver.timer.output_disable" value=""/>
      <property id="module.driver.timer.adc_trigger" value=""/>
      <property id="module.driver.timer.adc_a_compare_match" value="0"/>
      <property id="module.driver.timer.adc_b_compare_match" value="0"/>
      <property id="module.driver.timer.dead_time_count_up" value="0"/>
      <property id="module.driver.timer.dead_time_count_down" value="0"/>
      <property id="module.driver.timer.interrupt_skip.source" value="module.driver.timer.interrupt_skip.source.none"/>
      <property id="module.driver.timer.interrupt_skip.count" value="module.driver.timer.interrupt_skip.count.count_0"/>
      <property id="module.driver.timer.interrupt_skip.adc" value="module.driver.timer.interrupt_skip.skip_sources.interrupt_skip.adc.none"/>
      <property id="module.driver.timer.gtioca_disable_setting" value="module.driver.timer.gtioca_disable_setting.gtioc_disable_prohibited"/>
      <property id="module.driver.timer.gtiocb_disable_setting" value="module.driver.timer.gtiocb_disable_setting.gtioc_disable_prohibited"/>
    </module>
    <module id="module.driver.timer_on_gpt.1479290734">
      <property id="module.driver.timer.name" value="g_timer6"/>
      <property id="module.driver.timer.channel" value="6"/>
      <property id="module.driver.timer.mode" value="module.driver.timer.mode.mode_pwm"/>
      <property id="module.driver.timer.period" value="20"/>
      <property id="module.driver.timer.compare_match.a.status" value="module.driver.timer.compare_match.a.status.disabled"/>
      <property id="module.driver.timer.compare_match.a.value" value="0"/>
      <property id="module.driver.timer.compare_match.b.status" value="module.driver.timer.compare_match.b.status.disabled"/>
      <property id="module.driver.timer.compare_match.b.value" value="0"/>
      <property id="module.driver.timer.unit" value="module.driver.timer.unit.unit_frequency_khz"/>
      <property id="module.driver.timer.gtior.gtioa.initial_output_level" value="module.driver.timer.gtior.gtioa.initial_output_level.low"/>
      <property id="module.driver.timer.gtior.gtioa.cycle_end_output_level" value="module.driver.timer.gtior.gtioa.cycle_end_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtioa.compare_match_output_level" value="module.driver.timer.gtior.gtioa.compare_match_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtioa.count_stop_retain" value="module.driver.timer.gtior.gtioa.count_stop_retain.disabled"/>
      <property id="module.driver.timer.gtior.gtiob.initial_output_level" value="module.driver.timer.gtior.gtiob.initial_output_level.low"/>
      <property id="module.driver.timer.gtior.gtiob.cycle_end_output_level" value="module.driver.timer.gtior.gtiob.cycle_end_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtiob.compare_match_output_level" value="module.driver.timer.gtior.gtiob.compare_match_output_level.retain"/>
      <property id="module.driver.timer.gtior.gtiob.count_stop_retain" value="module.driver.timer.gtior.gtiob.count_stop_retain.disabled"/>
      <property id="module.driver.timer.gtior.custom_waveform_enable" value="module.driver.timer.gtior.custom_waveform_enable.disabled"/>
      <property id="module.driver.timer.duty_cycle" value="50"/>
      <property id="module.driver.timer.gtioca_output_enabled" value="module.driver.timer.gtioca_output_enabled.false"/>
      <property id="module.driver.timer.gtioca_stop_level" value="module.driver.timer.gtioca_stop_level.pin_level_low"/>
      <property id="module.driver.timer.gtiocb_output_enabled" value="module.driver.timer.gtiocb_output_enabled.true"/>
      <property id="module.driver.timer.gtiocb_stop_level" value="module.driver.timer.gtiocb_stop_level.pin_level_low"/>
      <property id="module.driver.timer.count_up_source" value=""/>
      <property id="module.driver.timer.count_down_source" value=""/>
      <property id="module.driver.timer.start_source" value=""/>
      <property id="module.driver.timer.stop_source" value=""/>
      <property id="module.driver.timer.clear_source" value=""/>
      <property id="module.driver.timer.capture_a_source" value=""/>
      <property id="module.driver.timer.capture_b_source" value=""/>
      <property id="module.driver.timer.gtioca_filter" value="module.driver.timer.gtioc_filter.gtioc_filter_none"/>
      <property id="module.driver.timer.gtiocb_filter" value="module.driver.timer.gtioc_filter.gtioc_filter_none"/>
      <property id="module.driver.timer.p_callback" value="NULL"/>
      <property id="module.driver.timer.ipl" value="_disabled"/>
      <property id="module.driver.timer.capture_a_ipl" value="_disabled"/>
      <property id="module.driver.timer.capture_b_ipl" value="_disabled"/>
      <property id="module.driver.timer.trough_ipl" value="_disabled"/>
      <property id="module.driver.timer.extra" value="module.driver.timer.extra.disabled"/>
      <property id="module.driver.timer.poeg_link" value="enum.driver.poeg.channels.poeg_link_poeg0"/>
      <property id="module.driver.timer.output_disable" value=""/>
      <property id="module.driver.timer.adc_trigger" value=""/>
      <property id="module.driver.timer.adc_a_compare_match" value="0"/>
      <property id="module.driver.timer.adc_b_compare_match" value="0"/>
      <property id="module.driver.timer.dead_time_count_up" value="0"/>
      <property id="module.driver.timer.dead_time_count_down" value="0"/>
      <property id="module.driver.timer.interrupt_skip.source" value="module.driver.timer.interrupt_skip.source.none"/>
      <property id="module.driver.timer.interrupt_skip.count" value="module.driver.timer.interrupt_skip.count.count_0"/>
      <property id="module.driver.timer.interrupt_skip.adc" value="module.driver.timer.interrupt_skip.skip_sources.interrupt_skip.adc.none"/>
      <property id="module.driver.timer.gtioca_disable_setting" value="module.driver.timer.gtioca_disable_setting.gtioc_disable_prohibited"/>
      <property id="module.driver.timer.gtiocb_disable_setting" value="module.driver.timer.gtiocb_disable_setting.gtioc_disable_prohibited"/>
    </module>
    <context id="_hal.0">
      <stack module="module.driver.ioport_on_ioport.0"/>
      <stack module="module.driver.uart_on_sci_uart.536471316"/>
      <stack module="module.driver.adc_on_adc.123925952"/>
      <stack module="module.driver.timer_on_gpt.379762189"/>
      <stack module="module.driver.timer_on_gpt.1479290734"/>
    </context>
    <config id="config.driver.adc">
      <property id="config.driver.adc.param_checking_enable" value="config.driver.adc.param_checking_enable.bsp"/>
    </config>
    <config id="config.driver.sci_uart">
      <property id="config.driver.sci_uart.param_checking_enable" value="config.driver.sci_uart.param_checking_enable.bsp"/>
      <property id="config.driver.sci_uart.fifo_support" value="config.driver.sci_uart.fifo_support.disabled"/>
      <property id="config.driver.sci_uart.dtc_support" value="config.driver.sci_uart.dtc_support.disabled"/>
      <property id="config.driver.sci_uart.flow_control" value="config.driver.sci_uart.flow_control.disabled"/>
      <property id="config.driver.sci_uart.rs485" value="config.driver.sci_uart.rs485.disabled"/>
      <property id="config.driver.sci_uart.irda" value="config.driver.sci_uart.irda.disabled"/>
    </config>
    <config id="config.driver.gpt">
      <property id="config.driver.gpt.param_checking_enable" value="config.driver.gpt.param_checking_enable.bsp"/>
      <property id="config.driver.gpt.output_support_enable" value="config.driver.gpt.output_support_enable.enabled"/>
      <property id="config.driver.gpt.write_protect_enable" value="config.driver.gpt.write_protect_enable.disabled"/>
    </config>
    <config id="config.driver.ioport">
      <property id="config.driver.ioport.checking" value="config.driver.ioport.checking.system"/>
    </config>
  </raModuleConfiguration>
  <raPinConfiguration>
    <pincfg active="true" name="R7FA6M5BH3CFC.pincfg" selected="true" symbol="g_bsp_pin_cfg">
      <configSetting altId="adc0.an00.p000" configurationId="adc0.an00"/>
      <configSetting altId="adc0.mode.custom" configurationId="adc0.mode"/>
      <configSetting altId="debug0.mode.swd" configurationId="debug0.mode"/>
      <configSetting altId="debug0.swclk.p300" configurationId="debug0.swclk"/>
      <configSetting altId="debug0.swdio.p108" configurationId="debug0.swdio"/>
      <configSetting altId="gpt6.gtiocb.p600" configurationId="gpt6.gtiocb"/>
      <configSetting altId="gpt6.mode.gtiocaorgtiocb.free" configurationId="gpt6.mode"/>
      <configSetting altId="irq0.mode.enabled" configurationId="irq0.mode"/>
      <configSetting altId="p000.asel" configurationId="p000"/>
      <configSetting altId="p000.gpio_mode.gpio_mode_an" configurationId="p000.gpio_mode"/>
      <configSetting altId="p004.input" configurationId="p004"/>
      <configSetting altId="p004.gpio_mode.gpio_mode_in" configurationId="p004.gpio_mode"/>
      <configSetting altId="p005.input" configurationId="p005"/>
      <configSetting altId="p005.gpio_mode.gpio_mode_in" configurationId="p005.gpio_mode"/>
      <configSetting altId="p108.debug0.swdio" configurationId="p108"/>
      <configSetting altId="p108.gpio_mode.gpio_mode_peripheral" configurationId="p108.gpio_mode"/>
      <configSetting altId="p300.debug0.swclk" configurationId="p300"/>
      <configSetting altId="p300.gpio_mode.gpio_mode_peripheral" configurationId="p300.gpio_mode"/>
      <configSetting altId="p400.output.high" configurationId="p400"/>
      <configSetting altId="p400.gpio_mode.gpio_mode_out.high" configurationId="p400.gpio_mode"/>
      <configSetting altId="p403.output.high" configurationId="p403"/>
      <configSetting altId="p403.gpio_mode.gpio_mode_out.high" configurationId="p403.gpio_mode"/>
      <configSetting altId="p404.output.high" configurationId="p404"/>
      <configSetting altId="p404.gpio_mode.gpio_mode_out.high" configurationId="p404.gpio_mode"/>
      <configSetting altId="p511.sci4.rxd" configurationId="p511"/>
      <configSetting altId="p511.gpio_mode.gpio_mode_peripheral" configurationId="p511.gpio_mode"/>
      <configSetting altId="p512.sci4.txd" configurationId="p512"/>
      <configSetting altId="p512.gpio_mode.gpio_mode_peripheral" configurationId="p512.gpio_mode"/>
      <configSetting altId="p600.gpt6.gtiocb" configurationId="p600"/>
      <configSetting altId="p600.gpio_mode.gpio_mode_peripheral" configurationId="p600.gpio_mode"/>
      <configSetting altId="p604.output.high" configurationId="p604"/>
      <configSetting altId="p604.gpio_mode.gpio_mode_out.high" configurationId="p604.gpio_mode"/>
      <configSetting altId="sci4.mode.asynchronous.free" configurationId="sci4.mode"/>
      <configSetting altId="sci4.rxd.p511" configurationId="sci4.rxd"/>
      <configSetting altId="sci4.txd.p512" configurationId="sci4.txd"/>
      <lockSetting id="adc0.an00" lock="true"/>
      <lockSetting id="gpt6.gtiocb" lock="true"/>
      <lockSetting id="sci4.rxd" lock="true"/>
      <lockSetting id="sci4.txd" lock="true"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
