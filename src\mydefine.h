#ifndef MYDEFINE_H_
#define MYDEFINE_H_

/* sys层 */
#include "hal_data.h"
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdarg.h>

/* bsp层 */
#include "systick/systick.h"
#include "time/gpt0_timing.h"
#include "time/gpt6_pwm.h"
#include "pcf8591/pcf8591.h"

/* 组件层 */
#include "circular_queue/circular_queue.h"
#include "scheduler/scheduler.h"
#include "task.h"

/* app层 */
#include "app/led/led_app.h"
#include "app/key/key_app.h"
#include "app/debug_uart/debug_uart.h"
#include "app/adc/adc_app.h"

/* 变量区 */
extern uint32_t uwTick;

#endif /* MYDEFINE_H_ */
