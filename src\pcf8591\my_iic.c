/*********************************************************************************************************************
 * Copyright (c) 2024, 逐飞科技
 * All rights reserved.
 *
 * 以下所有内容版权均属逐飞科技所有，未经允许不得用于商业用途，
 * 欢迎各位使用并传播本程序，修改内容时必须保留逐飞科技的版权声明。
 *
 * @file       		my_iic.c
 * @company	   		成都逐飞科技有限公司
 * <AUTHOR>
 * @version    		查看doc内version文件 版本说明
 * @Software 		e2studio
 * @Target core		RA6M5
 * @Taobao   		https://seekfree.taobao.com/
 * @date       		2024-02-18
 * @note
 * 				本文件是软件IIC驱动实现文件
 * 				基于GPIO模拟IIC时序，完全兼容标准IIC协议
 *
 * 				功能特点：
 * 				1. 支持标准IIC协议时序
 * 				2. 支持START/STOP条件生成
 * 				3. 支持ACK/NACK应答机制
 * 				4. 支持7位设备地址寻址
 * 				5. 支持单字节和多字节数据传输
 * 				6. 支持寄存器读写操作
 * 				7. 完善的错误处理和超时机制
 *
 * 				使用说明：
 * 				1. 调用my_iic_init()初始化IIC总线
 * 				2. 使用高级接口函数进行设备通信
 * 				3. 根据返回状态码判断操作结果
 ********************************************************************************************************************/

#include "my_iic.h"

//====================================================软件IIC 底层引脚操作函数====================================================
/*-------------------------------------------------------------------------------------------------------------------
 * @brief		SCL引脚输出高电平
 * @param		void
 * @return		void
 * @note  		内部函数，用于控制SCL时钟线输出高电平
 * 				在IIC通信中，SCL高电平期间数据有效
 * Sample usage:	my_iic_scl_high();		// SCL输出高电平
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_scl_high(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SCL_PIN, MY_IIC_HIGH);
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		SCL引脚输出低电平
 * @param		void
 * @return		void
 * @note  		内部函数，用于控制SCL时钟线输出低电平
 * 				在IIC通信中，SCL低电平期间允许数据变化
 * Sample usage:	my_iic_scl_low();		// SCL输出低电平
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_scl_low(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SCL_PIN, MY_IIC_LOW);
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		SDA引脚输出高电平
 * @param		void
 * @return		void
 * @note  		内部函数，用于控制SDA数据线输出高电平
 * 				在输出模式下，高电平表示数据位'1'或释放总线
 * Sample usage:	my_iic_sda_high();		// SDA输出高电平
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_sda_high(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SDA_PIN, MY_IIC_HIGH);
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		SDA引脚输出低电平
 * @param		void
 * @return		void
 * @note  		内部函数，用于控制SDA数据线输出低电平
 * 				在输出模式下，低电平表示数据位'0'或拉低总线
 * Sample usage:	my_iic_sda_low();		// SDA输出低电平
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_sda_low(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SDA_PIN, MY_IIC_LOW);
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		SDA引脚配置为输入模式
 * @param		void
 * @return		void
 * @note  		内部函数，将SDA引脚配置为输入模式，用于读取从机应答或数据
 * 				配置为输入模式时启用内部上拉电阻，确保总线空闲时为高电平
 * Sample usage:	my_iic_sda_input();		// SDA配置为输入模式
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_sda_input(void)
{
    R_BSP_PinAccessEnable();                                                     // 使能PFS寄存器写访问
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SDA_PIN,
                    IOPORT_CFG_PORT_DIRECTION_INPUT | IOPORT_CFG_PULLUP_ENABLE); // 配置为输入模式并使能上拉
    R_BSP_PinAccessDisable();                                                    // 禁用PFS寄存器写访问
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		SDA引脚配置为输出模式
 * @param		void
 * @return		void
 * @note  		内部函数，将SDA引脚配置为输出模式，用于发送数据或控制信号
 * 				配置为输出模式时设置为高驱动能力，确保信号质量
 * Sample usage:	my_iic_sda_output();	// SDA配置为输出模式
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_sda_output(void)
{
    R_BSP_PinAccessEnable();                                                     // 使能PFS寄存器写访问
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SDA_PIN,
                    IOPORT_CFG_PORT_DIRECTION_OUTPUT | IOPORT_CFG_DRIVE_HIGH);   // 配置为输出模式并设置高驱动
    R_BSP_PinAccessDisable();                                                    // 禁用PFS寄存器写访问
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		读取SDA引脚电平状态
 * @param		void
 * @return		uint8_t			引脚电平状态 (1-高电平, 0-低电平)
 * @note  		内部函数，读取SDA引脚当前电平状态
 * 				主要用于检测从机应答信号和读取数据位
 * Sample usage:	uint8_t level = my_iic_sda_read();	// 读取SDA电平
 -------------------------------------------------------------------------------------------------------------------*/
static uint8_t my_iic_sda_read(void)
{
    bsp_io_level_t level;
    R_IOPORT_PinRead(&g_ioport_ctrl, MY_IIC_SDA_PIN, &level);                   // 读取引脚电平
    return (level == MY_IIC_HIGH) ? 1 : 0;                                      // 转换为数字值返回
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		微秒级延时函数
 * @param		us				延时时间(微秒)
 * @return		void
 * @note  		内部函数，提供IIC时序所需的微秒级延时
 * 				延时精度取决于系统时钟频率，可根据实际情况调整循环次数
 * 				当前设置适用于200MHz系统时钟
 * Sample usage:	my_iic_delay_us(5);		// 延时5微秒
 -------------------------------------------------------------------------------------------------------------------*/
static void my_iic_delay_us(uint32_t us)
{
    volatile uint32_t i;
    for(i = 0; i < us * 50; i++);                                               // 循环延时，50为经验值，可根据实际调整
}

//====================================================软件IIC 对外接口函数====================================================
/*-------------------------------------------------------------------------------------------------------------------
 * @brief		软件IIC初始化函数
 * @param		void
 * @return		void
 * @note  		初始化IIC总线，配置SCL和SDA引脚
 * 				1. 配置SCL为输出模式，启用上拉电阻和高驱动能力
 * 				2. 配置SDA为输出模式，启用上拉电阻和高驱动能力
 * 				3. 设置总线初始状态为空闲(SCL和SDA均为高电平)
 * 				注意：硬件上需要外接4.7kΩ上拉电阻到VCC
 * Sample usage:	my_iic_init();			// 初始化IIC总线
 -------------------------------------------------------------------------------------------------------------------*/
void my_iic_init(void)
{
    R_BSP_PinAccessEnable();                                                     // 使能PFS寄存器写访问

    // 配置SCL为输出模式，启用上拉电阻和高驱动能力
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SCL_PIN,
                    IOPORT_CFG_PORT_DIRECTION_OUTPUT | IOPORT_CFG_PULLUP_ENABLE | IOPORT_CFG_DRIVE_HIGH);

    // 配置SDA为输出模式，启用上拉电阻和高驱动能力
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SDA_PIN,
                    IOPORT_CFG_PORT_DIRECTION_OUTPUT | IOPORT_CFG_PULLUP_ENABLE | IOPORT_CFG_DRIVE_HIGH);

    R_BSP_PinAccessDisable();                                                    // 禁用PFS寄存器写访问

    // 设置总线初始状态为空闲状态(SCL和SDA均为高电平)
    my_iic_scl_high();                                                           // SCL输出高电平
    my_iic_sda_high();                                                           // SDA输出高电平
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		产生IIC起始信号(START)
 * @param		void
 * @return		void
 * @note  		产生IIC协议规定的起始信号
 * 				起始信号定义：SCL为高电平期间，SDA由高电平变为低电平
 * 				时序要求：
 * 				1. 确保SDA为输出模式
 * 				2. SDA和SCL先置高电平
 * 				3. SDA拉低产生下降沿(起始信号)
 * 				4. SCL拉低准备数据传输
 * Sample usage:	my_iic_start();			// 产生起始信号
 -------------------------------------------------------------------------------------------------------------------*/
void my_iic_start(void)
{
    my_iic_sda_output();                                                         // 确保SDA为输出模式
    my_iic_sda_high();                                                           // SDA先置高电平
    my_iic_scl_high();                                                           // SCL置高电平
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定

    my_iic_sda_low();                                                            // SDA拉低产生下降沿(START信号)
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时满足建立时间
    my_iic_scl_low();                                                            // SCL拉低，准备数据传输
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		产生IIC停止信号(STOP)
 * @param		void
 * @return		void
 * @note  		产生IIC协议规定的停止信号
 * 				停止信号定义：SCL为高电平期间，SDA由低电平变为高电平
 * 				时序要求：
 * 				1. 确保SDA为输出模式
 * 				2. SCL和SDA先置低电平
 * 				3. SCL拉高
 * 				4. SDA拉高产生上升沿(停止信号)
 * Sample usage:	my_iic_stop();			// 产生停止信号
 -------------------------------------------------------------------------------------------------------------------*/
void my_iic_stop(void)
{
    my_iic_sda_output();                                                         // 确保SDA为输出模式
    my_iic_scl_low();                                                            // SCL先置低电平
    my_iic_sda_low();                                                            // SDA置低电平
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定

    my_iic_scl_high();                                                           // SCL拉高
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时满足建立时间
    my_iic_sda_high();                                                           // SDA拉高产生上升沿(STOP信号)
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定，总线回到空闲状态
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		发送ACK应答信号
 * @param		void
 * @return		void
 * @note  		主机向从机发送ACK(确认)应答信号
 * 				ACK信号定义：在第9个时钟周期，SDA保持低电平
 * 				时序要求：
 * 				1. 确保SDA为输出模式
 * 				2. SCL为低电平时，SDA拉低
 * 				3. SCL产生一个完整的时钟脉冲
 * 				通常用于主机接收数据后，告知从机继续发送下一字节
 * Sample usage:	my_iic_send_ack();		// 发送ACK应答
 -------------------------------------------------------------------------------------------------------------------*/
void my_iic_send_ack(void)
{
    my_iic_sda_output();                                                         // 确保SDA为输出模式
    my_iic_scl_low();                                                            // SCL保持低电平
    my_iic_sda_low();                                                            // SDA拉低表示ACK信号
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
    my_iic_scl_high();                                                           // SCL拉高，产生时钟上升沿
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时满足高电平时间
    my_iic_scl_low();                                                            // SCL拉低，完成时钟周期
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		发送NACK应答信号
 * @param		void
 * @return		void
 * @note  		主机向从机发送NACK(非确认)应答信号
 * 				NACK信号定义：在第9个时钟周期，SDA保持高电平
 * 				时序要求：
 * 				1. 确保SDA为输出模式
 * 				2. SCL为低电平时，SDA拉高
 * 				3. SCL产生一个完整的时钟脉冲
 * 				通常用于主机接收最后一字节后，告知从机停止发送
 * Sample usage:	my_iic_send_nack();		// 发送NACK应答
 -------------------------------------------------------------------------------------------------------------------*/
void my_iic_send_nack(void)
{
    my_iic_sda_output();                                                         // 确保SDA为输出模式
    my_iic_scl_low();                                                            // SCL保持低电平
    my_iic_sda_high();                                                           // SDA拉高表示NACK信号
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
    my_iic_scl_high();                                                           // SCL拉高，产生时钟上升沿
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时满足高电平时间
    my_iic_scl_low();                                                            // SCL拉低，完成时钟周期
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		等待从机ACK应答信号
 * @param		void
 * @return		uint8_t			应答结果 (0-收到ACK, 1-超时或收到NACK)
 * @note  		等待从机发送ACK应答信号，带超时检测
 * 				ACK检测原理：
 * 				1. 将SDA配置为输入模式，释放总线控制权
 * 				2. SCL产生时钟脉冲
 * 				3. 在SCL高电平期间检测SDA电平
 * 				4. SDA为低电平表示ACK，高电平表示NACK
 * 				5. 超时机制防止程序死锁
 * Sample usage:	if(my_iic_wait_ack())  { 处理错误 }
 -------------------------------------------------------------------------------------------------------------------*/
uint8_t my_iic_wait_ack(void)
{
    uint8_t timeout = 0;                                                         // 超时计数器

    my_iic_sda_input();                                                          // SDA配置为输入模式，释放总线
    my_iic_scl_low();                                                            // SCL先置低电平
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
    my_iic_scl_high();                                                           // SCL拉高，产生时钟上升沿
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定

    // 检测SDA电平，等待ACK信号(SDA为低电平)
    while(my_iic_sda_read())                                                     // 如果SDA为高电平(NACK或无响应)
    {
        timeout++;                                                               // 超时计数递增
        if(timeout > 250)                                                        // 超时检测
        {
            my_iic_stop();                                                       // 发送停止信号，释放总线
            return 1;                                                            // 返回错误(超时)
        }
        my_iic_delay_us(1);                                                      // 短延时后继续检测
    }

    my_iic_scl_low();                                                            // SCL拉低，完成时钟周期
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
    return 0;                                                                    // 返回成功(收到ACK)
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		发送一个字节数据
 * @param		data			要发送的字节数据
 * @return		void
 * @note  		通过IIC总线发送一个字节数据
 * 				发送时序：
 * 				1. 确保SDA为输出模式
 * 				2. 从最高位(MSB)开始逐位发送
 * 				3. SCL低电平时设置数据位
 * 				4. SCL高电平时数据有效
 * 				5. 发送完8位数据后等待从机应答
 * 				数据格式：8位数据，MSB先发送
 * Sample usage:	my_iic_send_byte(0x55);	// 发送字节0x55
 -------------------------------------------------------------------------------------------------------------------*/
void my_iic_send_byte(uint8_t data)
{
    uint8_t i;                                                                   // 位计数器

    my_iic_sda_output();                                                         // 确保SDA为输出模式
    my_iic_scl_low();                                                            // SCL保持低电平

    // 逐位发送数据，从最高位开始
    for(i = 0; i < 8; i++)                                                       // 发送8位数据
    {
        if(data & 0x80)                                                          // 检测最高位
            my_iic_sda_high();                                                   // 发送'1'
        else
            my_iic_sda_low();                                                    // 发送'0'

        data <<= 1;                                                              // 数据左移，准备下一位
        my_iic_delay_us(MY_IIC_DELAY_US);                                        // 延时满足建立时间
        my_iic_scl_high();                                                       // SCL拉高，数据有效
        my_iic_delay_us(MY_IIC_DELAY_US);                                        // 延时满足高电平时间
        my_iic_scl_low();                                                        // SCL拉低，准备下一位
        my_iic_delay_us(MY_IIC_DELAY_US);                                        // 延时稳定
    }
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		接收一个字节数据
 * @param		void
 * @return		uint8_t			接收到的字节数据
 * @note  		从IIC总线接收一个字节数据
 * 				接收时序：
 * 				1. 将SDA配置为输入模式，释放总线控制权
 * 				2. 从最高位(MSB)开始逐位接收
 * 				3. SCL低电平时准备接收
 * 				4. SCL高电平时读取数据位
 * 				5. 接收完8位数据后需要发送应答信号
 * 				数据格式：8位数据，MSB先接收
 * Sample usage:	uint8_t data = my_iic_read_byte();	// 接收一个字节
 -------------------------------------------------------------------------------------------------------------------*/
uint8_t my_iic_read_byte(void)
{
    uint8_t i, data = 0;                                                         // 位计数器和数据缓存

    my_iic_sda_input();                                                          // SDA配置为输入模式，释放总线

    // 逐位接收数据，从最高位开始
    for(i = 0; i < 8; i++)                                                       // 接收8位数据
    {
        my_iic_scl_low();                                                        // SCL拉低，准备接收
        my_iic_delay_us(MY_IIC_DELAY_US);                                        // 延时稳定
        my_iic_scl_high();                                                       // SCL拉高，数据有效
        data <<= 1;                                                              // 数据左移，为新位腾出空间
        if(my_iic_sda_read())                                                    // 读取SDA电平
            data |= 0x01;                                                        // 如果为高电平，设置最低位为1
        my_iic_delay_us(MY_IIC_DELAY_US);                                        // 延时满足高电平时间
    }

    my_iic_scl_low();                                                            // SCL拉低，完成接收
    my_iic_delay_us(MY_IIC_DELAY_US);                                            // 延时稳定
    return data;                                                                 // 返回接收到的数据
}

//====================================================软件IIC 高级接口函数====================================================
/*-------------------------------------------------------------------------------------------------------------------
 * @brief		向指定设备的寄存器写入数据
 * @param		device_addr		设备地址(7位地址，不包含读写位)
 * @param		reg_addr		寄存器地址
 * @param		data			要写入的数据
 * @return		my_iic_status_t	操作状态 (MY_IIC_SUCCESS-成功, MY_IIC_NACK-设备无应答)
 * @note  		标准的IIC寄存器写操作
 * 				操作流程：
 * 				1. 发送起始信号
 * 				2. 发送设备地址+写命令(R/W=0)
 * 				3. 等待设备应答
 * 				4. 发送寄存器地址
 * 				5. 等待设备应答
 * 				6. 发送数据
 * 				7. 等待设备应答
 * 				8. 发送停止信号
 * Sample usage:	my_iic_write_reg(0x48, 0x00, 0xFF);	// 向地址0x48设备的0x00寄存器写入0xFF
 -------------------------------------------------------------------------------------------------------------------*/
my_iic_status_t my_iic_write_reg(uint8_t device_addr, uint8_t reg_addr, uint8_t data)
{
    my_iic_start();                                                              // 发送起始信号

    // 发送设备地址+写命令 (7位设备地址左移1位 + 写命令位0)
    my_iic_send_byte(device_addr << 1 | MY_IIC_WRITE);                          // 发送设备地址和写命令
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    // 发送寄存器地址
    my_iic_send_byte(reg_addr);                                                  // 发送要写入的寄存器地址
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    // 发送数据
    my_iic_send_byte(data);                                                      // 发送要写入的数据
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    my_iic_stop();                                                               // 发送停止信号
    return MY_IIC_SUCCESS;                                                       // 操作成功
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		从指定设备的寄存器读取数据
 * @param		device_addr		设备地址(7位地址，不包含读写位)
 * @param		reg_addr		寄存器地址
 * @param		data			数据接收缓冲区指针
 * @return		my_iic_status_t	操作状态 (MY_IIC_SUCCESS-成功, MY_IIC_ERROR-参数错误, MY_IIC_NACK-设备无应答)
 * @note  		标准的IIC寄存器读操作
 * 				操作流程：
 * 				1. 发送起始信号
 * 				2. 发送设备地址+写命令(R/W=0)
 * 				3. 等待设备应答
 * 				4. 发送寄存器地址
 * 				5. 等待设备应答
 * 				6. 重新发送起始信号(重启动)
 * 				7. 发送设备地址+读命令(R/W=1)
 * 				8. 等待设备应答
 * 				9. 读取数据
 * 				10. 发送NACK应答(表示读取结束)
 * 				11. 发送停止信号
 * Sample usage:	uint8_t data; my_iic_read_reg(0x48, 0x00, &data);	// 从地址0x48设备的0x00寄存器读取数据
 -------------------------------------------------------------------------------------------------------------------*/
my_iic_status_t my_iic_read_reg(uint8_t device_addr, uint8_t reg_addr, uint8_t *data)
{
    if(data == NULL)                                                             // 参数检查
        return MY_IIC_ERROR;                                                     // 参数错误

    my_iic_start();                                                              // 发送起始信号

    // 第一阶段：写寄存器地址
    my_iic_send_byte(device_addr << 1 | MY_IIC_WRITE);                          // 发送设备地址和写命令
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    my_iic_send_byte(reg_addr);                                                  // 发送要读取的寄存器地址
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    // 第二阶段：重启动并读取数据
    my_iic_start();                                                              // 重新发送起始信号(重启动)

    my_iic_send_byte(device_addr << 1 | MY_IIC_READ);                           // 发送设备地址和读命令
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    // 读取数据
    *data = my_iic_read_byte();                                                  // 读取一个字节数据
    my_iic_send_nack();                                                          // 发送NACK，表示读取结束

    my_iic_stop();                                                               // 发送停止信号
    return MY_IIC_SUCCESS;                                                       // 操作成功
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		向指定设备写入多字节数据
 * @param		device_addr		设备地址(7位地址，不包含读写位)
 * @param		data			要发送的数据缓冲区指针
 * @param		len				数据长度(字节数)
 * @return		my_iic_status_t	操作状态 (MY_IIC_SUCCESS-成功, MY_IIC_ERROR-参数错误, MY_IIC_NACK-设备无应答)
 * @note  		连续写入多字节数据到设备
 * 				操作流程：
 * 				1. 发送起始信号
 * 				2. 发送设备地址+写命令(R/W=0)
 * 				3. 等待设备应答
 * 				4. 循环发送数据字节
 * 				5. 每发送一字节后等待设备应答
 * 				6. 发送停止信号
 * 				适用于向EEPROM等设备写入连续数据
 * Sample usage:	uint8_t buf[]={0x01,0x02,0x03}; my_iic_write_data(0x50, buf, 3);
 -------------------------------------------------------------------------------------------------------------------*/
my_iic_status_t my_iic_write_data(uint8_t device_addr, uint8_t *data, uint16_t len)
{
    uint16_t i;                                                                  // 循环计数器

    if(data == NULL || len == 0)                                                 // 参数检查
        return MY_IIC_ERROR;                                                     // 参数错误

    my_iic_start();                                                              // 发送起始信号

    // 发送设备地址+写命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_WRITE);                          // 发送设备地址和写命令
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    // 循环发送数据
    for(i = 0; i < len; i++)                                                     // 逐字节发送数据
    {
        my_iic_send_byte(data[i]);                                               // 发送当前字节
        if(my_iic_wait_ack())                                                    // 等待设备应答
            return MY_IIC_NACK;                                                  // 设备无应答，返回错误
    }

    my_iic_stop();                                                               // 发送停止信号
    return MY_IIC_SUCCESS;                                                       // 操作成功
}

/*-------------------------------------------------------------------------------------------------------------------
 * @brief		从指定设备读取多字节数据
 * @param		device_addr		设备地址(7位地址，不包含读写位)
 * @param		data			数据接收缓冲区指针
 * @param		len				要读取的数据长度(字节数)
 * @return		my_iic_status_t	操作状态 (MY_IIC_SUCCESS-成功, MY_IIC_ERROR-参数错误, MY_IIC_NACK-设备无应答)
 * @note  		从设备连续读取多字节数据
 * 				操作流程：
 * 				1. 发送起始信号
 * 				2. 发送设备地址+读命令(R/W=1)
 * 				3. 等待设备应答
 * 				4. 循环读取数据字节
 * 				5. 读取非最后字节后发送ACK应答
 * 				6. 读取最后字节后发送NACK应答
 * 				7. 发送停止信号
 * 				适用于从EEPROM等设备读取连续数据
 * Sample usage:	uint8_t buf[3]; my_iic_read_data(0x50, buf, 3);
 -------------------------------------------------------------------------------------------------------------------*/
my_iic_status_t my_iic_read_data(uint8_t device_addr, uint8_t *data, uint16_t len)
{
    uint16_t i;                                                                  // 循环计数器

    if(data == NULL || len == 0)                                                 // 参数检查
        return MY_IIC_ERROR;                                                     // 参数错误

    my_iic_start();                                                              // 发送起始信号

    // 发送设备地址+读命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_READ);                           // 发送设备地址和读命令
    if(my_iic_wait_ack())                                                        // 等待设备应答
        return MY_IIC_NACK;                                                      // 设备无应答，返回错误

    // 循环读取数据
    for(i = 0; i < len; i++)                                                     // 逐字节读取数据
    {
        data[i] = my_iic_read_byte();                                            // 读取当前字节
        if(i < len - 1)                                                          // 判断是否为最后一字节
            my_iic_send_ack();                                                   // 非最后字节，发送ACK继续读取
        else
            my_iic_send_nack();                                                  // 最后字节，发送NACK结束读取
    }

    my_iic_stop();                                                               // 发送停止信号
    return MY_IIC_SUCCESS;                                                       // 操作成功
}
