#include "my_iic.h"

/* 私有函数实现 */
static void my_iic_scl_high(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SCL_PIN, MY_IIC_HIGH);
}

static void my_iic_scl_low(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SCL_PIN, MY_IIC_LOW);
}

static void my_iic_sda_high(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SDA_PIN, MY_IIC_HIGH);
}

static void my_iic_sda_low(void)
{
    R_IOPORT_PinWrite(&g_ioport_ctrl, MY_IIC_SDA_PIN, MY_IIC_LOW);
}

static void my_iic_sda_input(void)
{
    R_BSP_PinAccessEnable();
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SDA_PIN, 
                    IOPORT_CFG_PORT_DIRECTION_INPUT | IOPORT_CFG_PULLUP_ENABLE);
    R_BSP_PinAccessDisable();
}

static void my_iic_sda_output(void)
{
    R_BSP_PinAccessEnable();
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SDA_PIN, 
                    IOPORT_CFG_PORT_DIRECTION_OUTPUT | IOPORT_CFG_DRIVE_HIGH);
    R_BSP_PinAccessDisable();
}

static uint8_t my_iic_sda_read(void)
{
    bsp_io_level_t level;
    R_IOPORT_PinRead(&g_ioport_ctrl, MY_IIC_SDA_PIN, &level);
    return (level == MY_IIC_HIGH) ? 1 : 0;
}

static void my_iic_delay_us(uint32_t us)
{
    volatile uint32_t i;
    for(i = 0; i < us * 50; i++); // 粗略延时,根据系统时钟调整
}

/* 公共函数实现 */
void my_iic_init(void)
{
    R_BSP_PinAccessEnable();
    
    // 配置SCL为输出,开漏,上拉
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SCL_PIN, 
                    IOPORT_CFG_PORT_DIRECTION_OUTPUT | IOPORT_CFG_PULLUP_ENABLE | IOPORT_CFG_DRIVE_HIGH);
    
    // 配置SDA为输出,开漏,上拉
    R_IOPORT_PinCfg(&g_ioport_ctrl, MY_IIC_SDA_PIN, 
                    IOPORT_CFG_PORT_DIRECTION_OUTPUT | IOPORT_CFG_PULLUP_ENABLE | IOPORT_CFG_DRIVE_HIGH);
    
    R_BSP_PinAccessDisable();
    
    // 初始状态设为高电平
    my_iic_scl_high();
    my_iic_sda_high();
    my_iic_delay_us(MY_IIC_DELAY_US);
}

void my_iic_start(void)
{
    my_iic_sda_output();
    my_iic_sda_high();
    my_iic_scl_high();
    my_iic_delay_us(MY_IIC_DELAY_US);
    
    my_iic_sda_low();  // START信号:SCL高电平时,SDA下降沿
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_scl_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
}

void my_iic_stop(void)
{
    my_iic_sda_output();
    my_iic_scl_low();
    my_iic_sda_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
    
    my_iic_scl_high();
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_sda_high(); // STOP信号:SCL高电平时,SDA上升沿
    my_iic_delay_us(MY_IIC_DELAY_US);
}

void my_iic_send_ack(void)
{
    my_iic_sda_output();
    my_iic_scl_low();
    my_iic_sda_low();  // ACK信号
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_scl_high();
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_scl_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
}

void my_iic_send_nack(void)
{
    my_iic_sda_output();
    my_iic_scl_low();
    my_iic_sda_high(); // NACK信号
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_scl_high();
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_scl_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
}

uint8_t my_iic_wait_ack(void)
{
    uint8_t timeout = 0;
    
    my_iic_sda_input();
    my_iic_scl_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
    my_iic_scl_high();
    my_iic_delay_us(MY_IIC_DELAY_US);
    
    while(my_iic_sda_read())
    {
        timeout++;
        if(timeout > 250)
        {
            my_iic_stop();
            return 1; // 超时,返回错误
        }
        my_iic_delay_us(1);
    }
    
    my_iic_scl_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
    return 0; // 收到ACK
}

void my_iic_send_byte(uint8_t data)
{
    uint8_t i;
    
    my_iic_sda_output();
    my_iic_scl_low();
    
    for(i = 0; i < 8; i++)
    {
        if(data & 0x80)
            my_iic_sda_high();
        else
            my_iic_sda_low();
        
        data <<= 1;
        my_iic_delay_us(MY_IIC_DELAY_US);
        my_iic_scl_high();
        my_iic_delay_us(MY_IIC_DELAY_US);
        my_iic_scl_low();
        my_iic_delay_us(MY_IIC_DELAY_US);
    }
}

uint8_t my_iic_read_byte(void)
{
    uint8_t i, data = 0;
    
    my_iic_sda_input();
    
    for(i = 0; i < 8; i++)
    {
        my_iic_scl_low();
        my_iic_delay_us(MY_IIC_DELAY_US);
        my_iic_scl_high();
        data <<= 1;
        if(my_iic_sda_read())
            data |= 0x01;
        my_iic_delay_us(MY_IIC_DELAY_US);
    }
    
    my_iic_scl_low();
    my_iic_delay_us(MY_IIC_DELAY_US);
    return data;
}

/* 高级接口函数 */
my_iic_status_t my_iic_write_reg(uint8_t device_addr, uint8_t reg_addr, uint8_t data)
{
    my_iic_start();

    // 发送设备地址+写命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_WRITE);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 发送寄存器地址
    my_iic_send_byte(reg_addr);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 发送数据
    my_iic_send_byte(data);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    my_iic_stop();
    return MY_IIC_SUCCESS;
}

my_iic_status_t my_iic_read_reg(uint8_t device_addr, uint8_t reg_addr, uint8_t *data)
{
    if(data == NULL)
        return MY_IIC_ERROR;

    my_iic_start();

    // 发送设备地址+写命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_WRITE);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 发送寄存器地址
    my_iic_send_byte(reg_addr);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 重新开始
    my_iic_start();

    // 发送设备地址+读命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_READ);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 读取数据
    *data = my_iic_read_byte();
    my_iic_send_nack();

    my_iic_stop();
    return MY_IIC_SUCCESS;
}

my_iic_status_t my_iic_write_data(uint8_t device_addr, uint8_t *data, uint16_t len)
{
    uint16_t i;

    if(data == NULL || len == 0)
        return MY_IIC_ERROR;

    my_iic_start();

    // 发送设备地址+写命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_WRITE);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 发送数据
    for(i = 0; i < len; i++)
    {
        my_iic_send_byte(data[i]);
        if(my_iic_wait_ack())
            return MY_IIC_NACK;
    }

    my_iic_stop();
    return MY_IIC_SUCCESS;
}

my_iic_status_t my_iic_read_data(uint8_t device_addr, uint8_t *data, uint16_t len)
{
    uint16_t i;

    if(data == NULL || len == 0)
        return MY_IIC_ERROR;

    my_iic_start();

    // 发送设备地址+读命令
    my_iic_send_byte(device_addr << 1 | MY_IIC_READ);
    if(my_iic_wait_ack())
        return MY_IIC_NACK;

    // 读取数据
    for(i = 0; i < len; i++)
    {
        data[i] = my_iic_read_byte();
        if(i < len - 1)
            my_iic_send_ack();
        else
            my_iic_send_nack();
    }

    my_iic_stop();
    return MY_IIC_SUCCESS;
}
