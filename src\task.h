#ifndef TASK_H_
#define TASK_H_

#include "mydefine.h"

typedef enum {
    CMD_UNKNOWN,    // 未知命令
    CMD_LED1_ON,    // 解析到 "led1-on" 命令
    CMD_LED1_OFF    // 解析到 "led1-off" 命令
    // 未来需要添加其他命令，可以在这里继续扩展
} ParsedCommand_t;

void adc_task(void);
void led_task(void);
void key_task(void);
void debug_uart4_task(void);
ParsedCommand_t parse_uart4_command(const char* command_buffer);

#endif /* TASK_H_ */
