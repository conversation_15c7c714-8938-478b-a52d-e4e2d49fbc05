Archive member included to satisfy reference by file (symbol)

d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
                              ./src/app/adc/adc_app.o (__aeabi_dmul)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
                              ./src/app/adc/adc_app.o (__aeabi_i2d)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
                              ./src/app/adc/adc_app.o (__aeabi_ddiv)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
                              ./src/task.o (__aeabi_dcmpge)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
                              ./src/time/gpt6_pwm.o (__aeabi_ldivmod)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
                              ./src/time/gpt6_pwm.o (__aeabi_uldivmod)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o) (__udivmoddi4)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o) (__aeabi_ldiv0)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
                              ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o (__errno)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o (exit)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o) (_global_impure_ptr)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o (__libc_init_array)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
                              ./ra/fsp/src/r_sci_uart/r_sci_uart.o (memcpy)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o (memset)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
                              (_printf_float)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o) (_printf_common)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
                              (_scanf_float)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o) (nanf)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o) (sprintf)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
                              ./src/task.o (sscanf)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o) (__seofread)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strlen.o)
                              ./src/task.o (strlen)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
                              ./src/task.o (strncmp)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o) (_strtod_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o) (_strtol_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
                              ./src/app/debug_uart/debug_uart.o (vsnprintf)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o) (_write_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o) (_close_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ctype_.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o) (_ctype_)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o) (_dtoa_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (__gethex)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (__match)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (_C_numeric_locale)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (__global_locale)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o) (_localeconv_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o) (_lseek_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o) (malloc)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o) (__ascii_mbtowc)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o) (memchr)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (_Balloc)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o) (_calloc_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o) (_free_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o) (_malloc_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o) (_svfprintf_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o) (__ssvfscanf_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o) (_scanf_chars)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o) (_read_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o) (errno)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (nan)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o) (_sbrk_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o) (__sccl)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strcmp.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o) (strcmp)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o) (_strtoul_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o) (__submore)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o) (__ascii_wctomb)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o) (_fflush_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o) (__sinit)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o) (_fwalk)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o) (__retarget_lock_init_recursive)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o) (memmove)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o) (__malloc_lock)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o) (_realloc_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o) (_malloc_usable_size_r)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o) (__aeabi_dcmpun)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o) (__aeabi_d2iz)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (__aeabi_d2uiz)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o) (__aeabi_d2f)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o) (__aeabi_d2lz)
d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
                              d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o) (__aeabi_d2ulz)

Discarded input sections

 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
 .data          0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
 .rodata        0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
 .text          0x00000000       0x7c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
 .ARM.extab     0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
 .ARM.exidx     0x00000000       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
 .ARM.attributes
                0x00000000       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
 .text          0x00000000        0x0 ./src/time/gpt0_timing.o
 .data          0x00000000        0x0 ./src/time/gpt0_timing.o
 .bss           0x00000000        0x0 ./src/time/gpt0_timing.o
 .text          0x00000000        0x0 ./src/time/gpt6_pwm.o
 .data          0x00000000        0x0 ./src/time/gpt6_pwm.o
 .bss           0x00000000        0x0 ./src/time/gpt6_pwm.o
 .text.gpt6_pwm_setduty
                0x00000000       0x38 ./src/time/gpt6_pwm.o
 .text          0x00000000        0x0 ./src/systick/systick.o
 .data          0x00000000        0x0 ./src/systick/systick.o
 .bss           0x00000000        0x0 ./src/systick/systick.o
 .text.SysTick_Delay
                0x00000000       0x2c ./src/systick/systick.o
 .text          0x00000000        0x0 ./src/scheduler/scheduler.o
 .data          0x00000000        0x0 ./src/scheduler/scheduler.o
 .bss           0x00000000        0x0 ./src/scheduler/scheduler.o
 .text          0x00000000        0x0 ./src/pcf8591/pcf8591.o
 .data          0x00000000        0x0 ./src/pcf8591/pcf8591.o
 .bss           0x00000000        0x0 ./src/pcf8591/pcf8591.o
 .debug_info    0x00000000       0x80 ./src/pcf8591/pcf8591.o
 .debug_abbrev  0x00000000       0x26 ./src/pcf8591/pcf8591.o
 .debug_aranges
                0x00000000       0x18 ./src/pcf8591/pcf8591.o
 .debug_line    0x00000000       0x1d ./src/pcf8591/pcf8591.o
 .debug_str     0x00000000      0x1d0 ./src/pcf8591/pcf8591.o
 .comment       0x00000000       0x4a ./src/pcf8591/pcf8591.o
 .ARM.attributes
                0x00000000       0x3a ./src/pcf8591/pcf8591.o
 .text          0x00000000        0x0 ./src/circular_queue/circular_queue.o
 .data          0x00000000        0x0 ./src/circular_queue/circular_queue.o
 .bss           0x00000000        0x0 ./src/circular_queue/circular_queue.o
 .text.Queue_Init
                0x00000000       0x14 ./src/circular_queue/circular_queue.o
 .text.Queue_isFull
                0x00000000       0x28 ./src/circular_queue/circular_queue.o
 .text.Queue_NoUse
                0x00000000       0x2c ./src/circular_queue/circular_queue.o
 .text          0x00000000        0x0 ./src/app/led/led_app.o
 .data          0x00000000        0x0 ./src/app/led/led_app.o
 .bss           0x00000000        0x0 ./src/app/led/led_app.o
 .text          0x00000000        0x0 ./src/app/key/key_app.o
 .data          0x00000000        0x0 ./src/app/key/key_app.o
 .bss           0x00000000        0x0 ./src/app/key/key_app.o
 .text          0x00000000        0x0 ./src/app/debug_uart/debug_uart.o
 .data          0x00000000        0x0 ./src/app/debug_uart/debug_uart.o
 .bss           0x00000000        0x0 ./src/app/debug_uart/debug_uart.o
 .bss.uart0_receive_complete_flag
                0x00000000        0x1 ./src/app/debug_uart/debug_uart.o
 .text          0x00000000        0x0 ./src/app/adc/adc_app.o
 .data          0x00000000        0x0 ./src/app/adc/adc_app.o
 .bss           0x00000000        0x0 ./src/app/adc/adc_app.o
 .text          0x00000000        0x0 ./src/hal_entry.o
 .data          0x00000000        0x0 ./src/hal_entry.o
 .bss           0x00000000        0x0 ./src/hal_entry.o
 .text          0x00000000        0x0 ./src/task.o
 .data          0x00000000        0x0 ./src/task.o
 .bss           0x00000000        0x0 ./src/task.o
 .text.parse_uart4_command
                0x00000000        0x8 ./src/task.o
 .text          0x00000000        0x0 ./ra_gen/common_data.o
 .data          0x00000000        0x0 ./ra_gen/common_data.o
 .bss           0x00000000        0x0 ./ra_gen/common_data.o
 .text.g_common_init
                0x00000000        0x4 ./ra_gen/common_data.o
 .text          0x00000000        0x0 ./ra_gen/hal_data.o
 .data          0x00000000        0x0 ./ra_gen/hal_data.o
 .bss           0x00000000        0x0 ./ra_gen/hal_data.o
 .text.g_hal_init
                0x00000000        0x4 ./ra_gen/hal_data.o
 .rodata.g_adc0
                0x00000000       0x10 ./ra_gen/hal_data.o
 .rodata.g_timer0
                0x00000000        0xc ./ra_gen/hal_data.o
 .rodata.g_timer6
                0x00000000        0xc ./ra_gen/hal_data.o
 .rodata.g_uart4
                0x00000000        0xc ./ra_gen/hal_data.o
 .text          0x00000000        0x0 ./ra_gen/main.o
 .data          0x00000000        0x0 ./ra_gen/main.o
 .bss           0x00000000        0x0 ./ra_gen/main.o
 .text          0x00000000        0x0 ./ra_gen/pin_data.o
 .data          0x00000000        0x0 ./ra_gen/pin_data.o
 .bss           0x00000000        0x0 ./ra_gen/pin_data.o
 .text          0x00000000        0x0 ./ra_gen/vector_data.o
 .data          0x00000000        0x0 ./ra_gen/vector_data.o
 .bss           0x00000000        0x0 ./ra_gen/vector_data.o
 .text          0x00000000        0x0 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .data          0x00000000        0x0 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .bss           0x00000000        0x0 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_Close
                0x00000000       0xcc ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_Read
                0x00000000        0xc ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_CallbackSet
                0x00000000       0x10 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_BaudSet
                0x00000000       0x68 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_InfoGet
                0x00000000        0xc ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_Abort
                0x00000000       0x3c ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_ReadStop
                0x00000000       0x14 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_BaudCalculate
                0x00000000      0x194 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .rodata.g_async_baud
                0x00000000        0xd ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .rodata.g_div_coefficient
                0x00000000       0x1a ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .rodata.g_uart_on_sci
                0x00000000       0x24 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text          0x00000000        0x0 ./ra/fsp/src/r_ioport/r_ioport.o
 .data          0x00000000        0x0 ./ra/fsp/src/r_ioport/r_ioport.o
 .bss           0x00000000        0x0 ./ra/fsp/src/r_ioport/r_ioport.o
 .text          0x00000000        0x0 ./ra/fsp/src/r_gpt/r_gpt.o
 .data          0x00000000        0x0 ./ra/fsp/src/r_gpt/r_gpt.o
 .bss           0x00000000        0x0 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_Stop
                0x00000000        0xc ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_Reset
                0x00000000        0xc ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_Enable
                0x00000000       0x38 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_Disable
                0x00000000       0x28 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_PeriodSet
                0x00000000       0x34 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_CompareMatchSet
                0x00000000       0x14 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_StatusGet
                0x00000000       0x18 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_CallbackSet
                0x00000000       0x10 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.r_gpt_ccmp_common_isr
                0x00000000       0xd4 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_Close
                0x00000000       0xa4 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_CounterSet
                0x00000000        0xc ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_OutputEnable
                0x00000000       0x20 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_OutputDisable
                0x00000000       0x20 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_AdcTriggerSet
                0x00000000       0x10 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_PwmOutputDelaySet
                0x00000000        0x4 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_PwmOutputDelayInitialize
                0x00000000        0x4 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.gpt_capture_compare_a_isr
                0x00000000        0x8 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.gpt_capture_compare_b_isr
                0x00000000        0x8 ./ra/fsp/src/r_gpt/r_gpt.o
 .rodata.g_timer_on_gpt
                0x00000000       0x34 ./ra/fsp/src/r_gpt/r_gpt.o
 .text          0x00000000        0x0 ./ra/fsp/src/r_adc/r_adc.o
 .data          0x00000000        0x0 ./ra/fsp/src/r_adc/r_adc.o
 .bss           0x00000000        0x0 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_CallbackSet
                0x00000000       0x10 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_ScanGroupStart
                0x00000000        0x4 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_ScanStop
                0x00000000        0xc ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_StatusGet
                0x00000000       0x10 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_Read32
                0x00000000       0x1c ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_Calibrate
                0x00000000        0x4 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_OffsetSet
                0x00000000        0x4 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_InfoGet
                0x00000000       0x80 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_Close
                0x00000000       0xf4 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_SampleStateCountSet
                0x00000000       0x14 ./ra/fsp/src/r_adc/r_adc.o
 .text.adc_scan_end_b_isr
                0x00000000        0x8 ./ra/fsp/src/r_adc/r_adc.o
 .text.adc_window_compare_isr
                0x00000000      0x11c ./ra/fsp/src/r_adc/r_adc.o
 .rodata.g_adc_on_adc
                0x00000000       0x34 ./ra/fsp/src/r_adc/r_adc.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.bsp_prv_operating_mode_set
                0x00000000       0x60 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.bsp_prv_prepare_pll
                0x00000000       0x14 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.prv_clock_dividers_set
                0x00000000        0xc ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.bsp_prv_clock_set
                0x00000000       0xc4 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.R_BSP_SubClockStabilizeWait
                0x00000000        0x8 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.R_BSP_OctaclkUpdate
                0x00000000       0x50 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text.R_BSP_SourceClockHzGet
                0x00000000        0xc ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .text.R_FSP_VersionGet
                0x00000000        0xc ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .text.R_BSP_GroupIrqWrite
                0x00000000       0x10 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .debug_info    0x00000000       0x79 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .debug_abbrev  0x00000000       0x26 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .debug_aranges
                0x00000000       0x18 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .debug_line    0x00000000       0x1d ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .debug_str     0x00000000      0x1d7 ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .comment       0x00000000       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .ARM.attributes
                0x00000000       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .rodata.g_interrupt_event_link_select
                0x00000000       0xc0 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .debug_info    0x00000000       0x79 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .debug_abbrev  0x00000000       0x26 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .debug_aranges
                0x00000000       0x18 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .debug_line    0x00000000       0x1d ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .debug_str     0x00000000      0x1d6 ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .comment       0x00000000       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .ARM.attributes
                0x00000000       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .debug_info    0x00000000       0x79 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .debug_abbrev  0x00000000       0x26 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .debug_aranges
                0x00000000       0x18 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .debug_line    0x00000000       0x1d ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .debug_str     0x00000000      0x1d7 ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .comment       0x00000000       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .ARM.attributes
                0x00000000       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .rodata.gp_start_of_nonsecure_callable_flash
                0x00000000        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .rodata.gp_start_of_nonsecure_callable_ram
                0x00000000        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .rodata.gp_start_of_nonsecure_data_flash
                0x00000000        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .rodata.gp_start_of_nonsecure_flash
                0x00000000        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .rodata.gp_start_of_nonsecure_ram
                0x00000000        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .debug_info    0x00000000      0x13c ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .debug_abbrev  0x00000000       0x6b ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .debug_aranges
                0x00000000       0x18 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .debug_line    0x00000000       0xf2 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .debug_str     0x00000000      0x2d7 ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .comment       0x00000000       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .ARM.attributes
                0x00000000       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_security.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .text          0x00000000        0x0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
 .data          0x00000000        0x0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
 .bss           0x00000000        0x0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
 .text.R_BSP_WarmStart
                0x00000000        0x4 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
 .text          0x00000000      0x254 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x00000000       0x30 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x00000000       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldf3.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .text          0x00000000       0xa0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .debug_frame   0x00000000       0x44 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .ARM.attributes
                0x00000000       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_ldivmod.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.exidx     0x00000000        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
 .text.exit     0x00000000       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
 .debug_frame   0x00000000       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-exit.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
 .rodata._global_impure_ptr
                0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
 .text.__libc_init_array
                0x00000000       0x48 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
 .debug_frame   0x00000000       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-init.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
 .text._sprintf_r
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
 .text._sscanf_r
                0x00000000       0x54 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .text.__sread  0x00000000       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .text.__swrite
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .text.__sseek  0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .text.__sclose
                0x00000000        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strlen.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strlen.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .text.strtod_l
                0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .text.strtod   0x00000000       0x18 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .text.strtof_l
                0x00000000       0xb4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .text.strtof   0x00000000       0xb8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .text.strtol_l
                0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .text.strtol   0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
 .text._write_r
                0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
 .debug_frame   0x00000000       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-writer.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
 .text._close_r
                0x00000000       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
 .debug_frame   0x00000000       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-closer.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ctype_.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ctype_.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ctype_.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 .text.__numeric_load_locale
                0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .text._setlocale_r
                0x00000000       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .text.__locale_mb_cur_max
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .text.setlocale
                0x00000000       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .bss._PathLocale
                0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .text.__localeconv_l
                0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .text.localeconv
                0x00000000        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
 .text._lseek_r
                0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
 .debug_frame   0x00000000       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lseekr.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
 .text.free     0x00000000       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
 .text._mbtowc_r
                0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .text._mprec_log10
                0x00000000       0x3c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .rodata.__mprec_tinytens
                0x00000000       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .text.__ssprint_r
                0x00000000       0xf6 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
 .text._read_r  0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
 .debug_frame   0x00000000       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-readr.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .text.cleanup_glue
                0x00000000       0x1a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .text._reclaim_reent
                0x00000000       0xb8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
 .text          0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strcmp.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strcmp.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strcmp.o)
 .debug_frame   0x00000000       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strcmp.o)
 .ARM.attributes
                0x00000000       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strcmp.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .text.strtoul_l
                0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .text.strtoul  0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .text._ungetc_r
                0x00000000      0x130 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .text.ungetc   0x00000000       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
 .text._wctomb_r
                0x00000000       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .text.__sflush_r
                0x00000000      0x108 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .text._fflush_r
                0x00000000       0x78 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .text.fflush   0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .debug_frame   0x00000000       0x5c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fflush.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.std      0x00000000       0x48 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text._cleanup_r
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__fp_lock
                0x00000000       0x18 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__fp_unlock
                0x00000000       0x18 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sfmoreglue
                0x00000000       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text._cleanup
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sfp_lock_acquire
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sfp_lock_release
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sinit_lock_acquire
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sinit_lock_release
                0x00000000        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sinit  0x00000000       0x70 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__sfp    0x00000000       0x8c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__fp_lock_all
                0x00000000       0x1c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text.__fp_unlock_all
                0x00000000       0x1c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .text._fwalk   0x00000000       0x3a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .text._fwalk_reent
                0x00000000       0x3e d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .debug_frame   0x00000000       0x54 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .ARM.attributes
                0x00000000       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-fwalk.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_init
                0x00000000        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_init_recursive
                0x00000000        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_close
                0x00000000        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_close_recursive
                0x00000000        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_acquire
                0x00000000        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire
                0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_try_acquire_recursive
                0x00000000        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text.__retarget_lock_release
                0x00000000        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___arc4random_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___at_quick_exit_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___atexit_recursive_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___dd_hash_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___env_recursive_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___sfp_recursive_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___sinit_recursive_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .bss.__lock___tz_mutex
                0x00000000        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
 .rodata        0x00000000       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
 .text          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o
 .data          0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o
 .bss           0x00000000        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o

Memory Configuration

Name             Origin             Length             Attributes
ITCM             0x00000000         0x00000000         xr
DTCM             0x00000000         0x00000000         xrw
FLASH            0x00000000         0x00200000         xr
RAM              0x20000000         0x00080000         xrw
DATA_FLASH       0x08000000         0x00002000         xr
QSPI_FLASH       0x60000000         0x04000000         xr
OSPI_DEVICE_0    0x68000000         0x08000000         xr
OSPI_DEVICE_1    0x70000000         0x10000000         xr
OSPI_DEVICE_0_RAM 0x68000000         0x08000000         xrw
OSPI_DEVICE_1_RAM 0x70000000         0x10000000         xrw
SDRAM            0x80010000         0x00000000         xrw
OPTION_SETTING   0x0100a100         0x00000100         r
OPTION_SETTING_OFS 0x0100a100         0x00000018         r
OPTION_SETTING_SAS 0x0100a134         0x000000cc         r
OPTION_SETTING_S 0x0100a200         0x00000100         r
ID_CODE          0x00000000         0x00000000         xr
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard/crt0.o
START GROUP
LOAD ./src/time/gpt0_timing.o
LOAD ./src/time/gpt6_pwm.o
LOAD ./src/systick/systick.o
LOAD ./src/scheduler/scheduler.o
LOAD ./src/pcf8591/pcf8591.o
LOAD ./src/circular_queue/circular_queue.o
LOAD ./src/app/led/led_app.o
LOAD ./src/app/key/key_app.o
LOAD ./src/app/debug_uart/debug_uart.o
LOAD ./src/app/adc/adc_app.o
LOAD ./src/hal_entry.o
LOAD ./src/task.o
LOAD ./ra_gen/common_data.o
LOAD ./ra_gen/hal_data.o
LOAD ./ra_gen/main.o
LOAD ./ra_gen/pin_data.o
LOAD ./ra_gen/vector_data.o
LOAD ./ra/fsp/src/r_sci_uart/r_sci_uart.o
LOAD ./ra/fsp/src/r_ioport/r_ioport.o
LOAD ./ra/fsp/src/r_gpt/r_gpt.o
LOAD ./ra/fsp/src/r_adc/r_adc.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_common.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_guard.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_io.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_macl.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o
LOAD ./ra/fsp/src/bsp/mcu/all/bsp_security.o
LOAD ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
LOAD ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
END GROUP
START GROUP
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a
END GROUP
START GROUP
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libc_nano.a
END GROUP
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o
                0x20000000                RAM_START = 0x20000000
                0x00080000                RAM_LENGTH = 0x80000
                0x00000000                FLASH_START = 0x0
                0x00200000                FLASH_LENGTH = 0x200000
                0x08000000                DATA_FLASH_START = 0x8000000
                0x00002000                DATA_FLASH_LENGTH = 0x2000
                0x0100a100                OPTION_SETTING_START = 0x100a100
                0x00000100                OPTION_SETTING_LENGTH = 0x100
                0x0100a200                OPTION_SETTING_S_START = 0x100a200
                0x00000100                OPTION_SETTING_S_LENGTH = 0x100
                0x00000000                ID_CODE_START = 0x0
                0x00000000                ID_CODE_LENGTH = 0x0
                0x80010000                SDRAM_START = 0x80010000
                0x00000000                SDRAM_LENGTH = 0x0
                0x60000000                QSPI_FLASH_START = 0x60000000
                0x04000000                QSPI_FLASH_LENGTH = 0x4000000
                0x68000000                OSPI_DEVICE_0_START = 0x68000000
                0x08000000                OSPI_DEVICE_0_LENGTH = 0x8000000
                0x70000000                OSPI_DEVICE_1_START = 0x70000000
                0x10000000                OSPI_DEVICE_1_LENGTH = 0x10000000
                0x04000000                QSPI_FLASH_PRV_LENGTH = DEFINED (QSPI_FLASH_SIZE)?ABSOLUTE (QSPI_FLASH_SIZE):ABSOLUTE (QSPI_FLASH_LENGTH)
                0x08000000                OSPI_DEVICE_0_PRV_LENGTH = DEFINED (OSPI_DEVICE_0_SIZE)?ABSOLUTE (OSPI_DEVICE_0_SIZE):ABSOLUTE (OSPI_DEVICE_0_LENGTH)
                0x10000000                OSPI_DEVICE_1_PRV_LENGTH = DEFINED (OSPI_DEVICE_1_SIZE)?ABSOLUTE (OSPI_DEVICE_1_SIZE):ABSOLUTE (OSPI_DEVICE_1_LENGTH)
                0x00000000                __RESERVE_NS_RAM = ((! (DEFINED (PROJECT_NONSECURE)) && DEFINED (RAM_NS_BUFFER_LENGTH)) && (OPTION_SETTING_S_LENGTH != 0x0))
                0x00000000                ITCM_START = DEFINED (ITCM_START)?ITCM_START:0x0
                0x00000000                ITCM_LENGTH = DEFINED (ITCM_LENGTH)?ITCM_LENGTH:0x0
                0x00000000                DTCM_START = DEFINED (DTCM_START)?DTCM_START:0x0
                0x00000000                DTCM_LENGTH = DEFINED (DTCM_LENGTH)?DTCM_LENGTH:0x0
                0x00000000                RAM_NS_BUFFER_BLOCK_LENGTH = DEFINED (RAM_NS_BUFFER_LENGTH)?ALIGN (RAM_NS_BUFFER_LENGTH, 0x2000):0x0
                0x00000000                RAM_NS_BUFFER_LENGTH = DEFINED (RAM_NS_BUFFER_LENGTH)?RAM_NS_BUFFER_LENGTH:0x0
                0x20080000                RAM_NS_BUFFER_START = ((RAM_START + RAM_LENGTH) - RAM_NS_BUFFER_LENGTH)
                0x20080000                RAM_NS_BUFFER_BLOCK_START = ((RAM_START + RAM_LENGTH) - RAM_NS_BUFFER_BLOCK_LENGTH)
                0x0100a180                OPTION_SETTING_START_NS = DEFINED (PROJECT_NONSECURE)?OPTION_SETTING_START:(OPTION_SETTING_START + 0x80)
                0x00000001                __bl_FSP_BOOTABLE_IMAGE = 0x1
                0x00000001                __bln_FSP_BOOTABLE_IMAGE = 0x1
                0x00000001                PROJECT_SECURE_OR_FLAT = (((! (DEFINED (PROJECT_NONSECURE)) || DEFINED (PROJECT_SECURE)) && OPTION_SETTING_LENGTH) && ! (DEFINED (FSP_BOOTABLE_IMAGE)))
                0x00000000                USE_OPTION_SETTING_NS = (DEFINED (PROJECT_NONSECURE) && ! (DEFINED (FSP_BOOTABLE_IMAGE)))
                0x00000000                __bl_FLASH_IMAGE_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_IMAGE_NUMBER == 0x1)?((FLASH_START + FLASH_BOOTLOADER_LENGTH) + FLASH_BOOTLOADER_HEADER_LENGTH):(DEFINED (BOOTLOADER_SECONDARY_USE_QSPI) || DEFINED (BOOTLOADER_SECONDARY_USE_OSPI_B))?(((FLASH_START + FLASH_BOOTLOADER_LENGTH) + FLASH_BOOTLOADER_SCRATCH_LENGTH) + FLASH_BOOTLOADER_HEADER_LENGTH):((((FLASH_START + FLASH_BOOTLOADER_LENGTH) + FLASH_BOOTLOADER_SCRATCH_LENGTH) + FLASH_APPLICATION_S_LENGTH) + FLASH_BOOTLOADER_HEADER_LENGTH)
                0x00000000                __bl_FLASH_IMAGE_LENGTH = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_S_LENGTH - FLASH_BOOTLOADER_HEADER_LENGTH)
                0x00000000                __bl_FLASH_IMAGE_END = (__bl_FLASH_IMAGE_START + __bl_FLASH_IMAGE_LENGTH)
                0x00000000                __bl_XIP_SECONDARY_FLASH_IMAGE_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(((FLASH_START + FLASH_BOOTLOADER_LENGTH) + FLASH_APPLICATION_S_LENGTH) + FLASH_BOOTLOADER_HEADER_LENGTH)
                0x00000000                __bl_XIP_SECONDARY_FLASH_IMAGE_END = (__bl_XIP_SECONDARY_FLASH_IMAGE_START + __bl_FLASH_IMAGE_LENGTH)
                0x00000000                __bl_FLASH_IMAGE_START_FROM_MMF_REGION = DEFINED (BOOT_IMAGE_FROM_MMF_REGION)?BOOT_IMAGE_FROM_MMF_REGION:0x0
                0x00000000                __bl_MEMORY_MIRROR_REGION_START = DEFINED (MMF_REGION_START_ADDR)?MMF_REGION_START_ADDR:0x0
                0x00000000                __bl_FLASH_NS_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_NS_LENGTH == 0x0)?__bl_FLASH_IMAGE_END:((__bl_FLASH_IMAGE_START - FLASH_BOOTLOADER_HEADER_LENGTH) + FLASH_APPLICATION_S_LENGTH)
                0x00000000                __bl_FLASH_NSC_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_NS_LENGTH == 0x0)?__bl_FLASH_IMAGE_END:(__bl_FLASH_IMAGE_END - FLASH_APPLICATION_NSC_LENGTH)
                0x00000000                __bl_RAM_NS_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_NS_LENGTH == 0x0)?(RAM_START + RAM_LENGTH):((RAM_START + RAM_LENGTH) - RAM_APPLICATION_NS_LENGTH)
                0x00000000                __bl_RAM_NSC_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_NS_LENGTH == 0x0)?(RAM_START + RAM_LENGTH):(__bl_RAM_NS_START - RAM_APPLICATION_NSC_LENGTH)
                0x00000000                __bl_FLASH_NS_IMAGE_START = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_NS_LENGTH == 0x0)?__bl_FLASH_IMAGE_END:(__bl_FLASH_NS_START + FLASH_BOOTLOADER_HEADER_LENGTH_2)
                0x00000000                __bln_FLASH_IMAGE_START = (__bl_FLASH_NS_IMAGE_START | ! (DEFINED (NS_OFFSET_START))?0x0:NS_OFFSET_START)
                0x00000000                __bln_FLASH_IMAGE_LENGTH = ! (DEFINED (FLASH_BOOTLOADER_LENGTH))?0x0:(FLASH_APPLICATION_NS_LENGTH == 0x0)?__bl_FLASH_IMAGE_END:(FLASH_APPLICATION_NS_LENGTH - FLASH_BOOTLOADER_HEADER_LENGTH_2)
                0x00000000                XIP_SECONDARY_SLOT_IMAGE = DEFINED (XIP_SECONDARY_SLOT_IMAGE)?XIP_SECONDARY_SLOT_IMAGE:0x0
                0x00000000                FLASH_IMAGE_START_FROM_MMF_REGION = ! (DEFINED (FLASH_IMAGE_START_FROM_MMF_REGION))?0x0:FLASH_IMAGE_START_FROM_MMF_REGION
                0x00000000                MEMORY_MIRROR_REGION_START = ! (DEFINED (MEMORY_MIRROR_REGION_START))?0x0:MEMORY_MIRROR_REGION_START
                0x00000000                FLASH_ORIGIN = ! (DEFINED (FLASH_IMAGE_START))?FLASH_START:(XIP_SECONDARY_SLOT_IMAGE == 0x1)?XIP_SECONDARY_FLASH_IMAGE_START:(FLASH_IMAGE_START_FROM_MMF_REGION == 0x1)?MEMORY_MIRROR_REGION_START:FLASH_IMAGE_START
                0x00200000                LIMITED_FLASH_LENGTH = DEFINED (FLASH_IMAGE_LENGTH)?FLASH_IMAGE_LENGTH:DEFINED (FLASH_BOOTLOADER_LENGTH)?FLASH_BOOTLOADER_LENGTH:FLASH_LENGTH
                0x00000034                OPTION_SETTING_SAS_SIZE = 0x34
                0x000000cc                OPTION_SETTING_SAS_LENGTH = ! (DEFINED (OPTION_SETTING_LENGTH))?0x0:(OPTION_SETTING_LENGTH == 0x0)?0x0:(OPTION_SETTING_LENGTH - OPTION_SETTING_SAS_SIZE)
START GROUP
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libc.a
LOAD d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libm.a
END GROUP

.text           0x00000000     0x7fd0
                0x00000000                __tz_FLASH_S = ABSOLUTE (FLASH_START)
                0x00000000                __ROM_Start = .
 *(.fixed_vectors*)
 .fixed_vectors
                0x00000000       0x40 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
                0x00000000                __Vectors
 *(.application_vectors*)
 .application_vectors
                0x00000040      0x180 ./ra_gen/vector_data.o
                0x00000040                g_vector_table
                0x000001c0                __Vectors_End = .
 *(.flash_gap*)
                0x000001c0                . = (OPTION_SETTING_LENGTH > 0x0)?.:(__ROM_Start + 0x400)
 *(.rom_registers*)
 *(.mcuboot_sce9_key*)
 *(.text*)
 .text          0x000001c0       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
 .text.gpt0_timing_init
                0x00000200       0x20 ./src/time/gpt0_timing.o
                0x00000200                gpt0_timing_init
 .text.gpt0_timing_callback
                0x00000220       0x1c ./src/time/gpt0_timing.o
                0x00000220                gpt0_timing_callback
 .text.gpt6_pwm_init
                0x0000023c       0x44 ./src/time/gpt6_pwm.o
                0x0000023c                gpt6_pwm_init
 .text.SysTick_Init
                0x00000280       0x54 ./src/systick/systick.o
                0x00000280                SysTick_Init
 .text.SysTick_Handler
                0x000002d4       0x50 ./src/systick/systick.o
                0x000002d4                SysTick_Handler
 .text.scheduler_init
                0x00000324       0x28 ./src/scheduler/scheduler.o
                0x00000324                scheduler_init
 .text.scheduler_run
                0x0000034c       0x4c ./src/scheduler/scheduler.o
                0x0000034c                scheduler_run
 .text.Queue_isEmpty
                0x00000398       0x10 ./src/circular_queue/circular_queue.o
                0x00000398                Queue_isEmpty
 .text.Queue_HadUse
                0x000003a8       0x24 ./src/circular_queue/circular_queue.o
                0x000003a8                Queue_HadUse
 .text.Queue_Wirte
                0x000003cc       0x6c ./src/circular_queue/circular_queue.o
                0x000003cc                Queue_Wirte
 .text.Queue_Read
                0x00000438       0x68 ./src/circular_queue/circular_queue.o
                0x00000438                Queue_Read
 .text.led_init
                0x000004a0       0x14 ./src/app/led/led_app.o
                0x000004a0                led_init
 .text.beep_on  0x000004b4       0x10 ./src/app/led/led_app.o
                0x000004b4                beep_on
 .text.beep_off
                0x000004c4       0x10 ./src/app/led/led_app.o
                0x000004c4                beep_off
 .text.key_init
                0x000004d4       0x2c ./src/app/key/key_app.o
                0x000004d4                key_init
 .text.key_read
                0x00000500       0x3c ./src/app/key/key_app.o
                0x00000500                key_read
 .text.debug_uart4_Init
                0x0000053c       0x30 ./src/app/debug_uart/debug_uart.o
                0x0000053c                debug_uart4_Init
 .text.debug_uart4_callback
                0x0000056c       0x28 ./src/app/debug_uart/debug_uart.o
                0x0000056c                debug_uart4_callback
 .text.my_printf
                0x00000594       0x54 ./src/app/debug_uart/debug_uart.o
                0x00000594                my_printf
 .text.adc_init
                0x000005e8       0x3c ./src/app/adc/adc_app.o
                0x000005e8                adc_init
 .text.adc_callback
                0x00000624        0xc ./src/app/adc/adc_app.o
                0x00000624                adc_callback
 .text.Read_ADC_Voltage_Value
                0x00000630       0x60 ./src/app/adc/adc_app.o
                0x00000630                Read_ADC_Voltage_Value
 .text.hal_entry
                0x00000690       0x54 ./src/hal_entry.o
                0x00000690                hal_entry
 .text.R_BSP_WarmStart
                0x000006e4       0x18 ./src/hal_entry.o
                0x000006e4                R_BSP_WarmStart
 .text.parse_uart4_command.part.0
                0x000006fc       0x7c ./src/task.o
 .text.adc_task
                0x00000778       0x40 ./src/task.o
                0x00000778                adc_task
 .text.led_task
                0x000007b8        0x4 ./src/task.o
                0x000007b8                led_task
 .text.key_task
                0x000007bc       0xa4 ./src/task.o
                0x000007bc                key_task
 .text.debug_uart4_task
                0x00000860       0xa4 ./src/task.o
                0x00000860                debug_uart4_task
 .text.startup.main
                0x00000904        0xc ./ra_gen/main.o
                0x00000904                main
 .text.R_SCI_UART_Write
                0x00000910       0x50 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                0x00000910                R_SCI_UART_Write
 .text.r_sci_uart_call_callback
                0x00000960       0x44 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .text.R_SCI_UART_Open
                0x000009a4      0x3a8 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                0x000009a4                R_SCI_UART_Open
 .text.sci_uart_txi_isr
                0x00000d4c       0x90 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                0x00000d4c                sci_uart_txi_isr
 .text.sci_uart_rxi_isr
                0x00000ddc       0x90 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                0x00000ddc                sci_uart_rxi_isr
 .text.sci_uart_tei_isr
                0x00000e6c       0x48 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                0x00000e6c                sci_uart_tei_isr
 .text.sci_uart_eri_isr
                0x00000eb4       0x78 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                0x00000eb4                sci_uart_eri_isr
 .text.R_IOPORT_Close
                0x00000f2c        0xc ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000f2c                R_IOPORT_Close
 .text.R_IOPORT_PinRead
                0x00000f38       0x24 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000f38                R_IOPORT_PinRead
 .text.R_IOPORT_PortRead
                0x00000f5c       0x14 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000f5c                R_IOPORT_PortRead
 .text.R_IOPORT_PortWrite
                0x00000f70       0x1c ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000f70                R_IOPORT_PortWrite
 .text.R_IOPORT_PinWrite
                0x00000f8c       0x20 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000f8c                R_IOPORT_PinWrite
 .text.R_IOPORT_PortDirectionSet
                0x00000fac       0x2c ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000fac                R_IOPORT_PortDirectionSet
 .text.R_IOPORT_PortEventInputRead
                0x00000fd8       0x18 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000fd8                R_IOPORT_PortEventInputRead
 .text.R_IOPORT_PinEventInputRead
                0x00000ff0       0x38 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00000ff0                R_IOPORT_PinEventInputRead
 .text.R_IOPORT_PortEventOutputWrite
                0x00001028       0x1c ./ra/fsp/src/r_ioport/r_ioport.o
                0x00001028                R_IOPORT_PortEventOutputWrite
 .text.R_IOPORT_PinEventOutputWrite
                0x00001044       0x2c ./ra/fsp/src/r_ioport/r_ioport.o
                0x00001044                R_IOPORT_PinEventOutputWrite
 .text.bsp_vbatt_init
                0x00001070       0xf4 ./ra/fsp/src/r_ioport/r_ioport.o
 .text.r_ioport_pins_config
                0x00001164       0xb4 ./ra/fsp/src/r_ioport/r_ioport.o
 .text.R_IOPORT_Open
                0x00001218       0x14 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00001218                R_IOPORT_Open
 .text.R_IOPORT_PinsCfg
                0x0000122c        0xc ./ra/fsp/src/r_ioport/r_ioport.o
                0x0000122c                R_IOPORT_PinsCfg
 .text.R_IOPORT_PinCfg
                0x00001238       0xa8 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00001238                R_IOPORT_PinCfg
 .text.R_GPT_Start
                0x000012e0        0xc ./ra/fsp/src/r_gpt/r_gpt.o
                0x000012e0                R_GPT_Start
 .text.r_gpt_call_callback
                0x000012ec       0x3c ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_InfoGet
                0x00001328       0x44 ./ra/fsp/src/r_gpt/r_gpt.o
                0x00001328                R_GPT_InfoGet
 .text.gpt_calculate_duty_cycle.isra.0
                0x0000136c       0x64 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_DutyCycleSet
                0x000013d0       0xd8 ./ra/fsp/src/r_gpt/r_gpt.o
                0x000013d0                R_GPT_DutyCycleSet
 .text.r_gpt_enable_irq
                0x000014a8       0x64 ./ra/fsp/src/r_gpt/r_gpt.o
 .text.R_GPT_Open
                0x0000150c      0x244 ./ra/fsp/src/r_gpt/r_gpt.o
                0x0000150c                R_GPT_Open
 .text.gpt_counter_overflow_isr
                0x00001750       0x94 ./ra/fsp/src/r_gpt/r_gpt.o
                0x00001750                gpt_counter_overflow_isr
 .text.R_ADC_ScanStart
                0x000017e4        0xc ./ra/fsp/src/r_adc/r_adc.o
                0x000017e4                R_ADC_ScanStart
 .text.R_ADC_Read
                0x000017f0       0x10 ./ra/fsp/src/r_adc/r_adc.o
                0x000017f0                R_ADC_Read
 .text.r_adc_irq_enable
                0x00001800       0x64 ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_Open
                0x00001864      0x124 ./ra/fsp/src/r_adc/r_adc.o
                0x00001864                R_ADC_Open
 .text.r_adc_call_callback
                0x00001988       0x54 ./ra/fsp/src/r_adc/r_adc.o
 .text.r_adc_scan_end_common_isr
                0x000019dc       0x5c ./ra/fsp/src/r_adc/r_adc.o
 .text.R_ADC_ScanCfg
                0x00001a38      0x200 ./ra/fsp/src/r_adc/r_adc.o
                0x00001a38                R_ADC_ScanCfg
 .text.adc_scan_end_isr
                0x00001c38        0x8 ./ra/fsp/src/r_adc/r_adc.o
                0x00001c38                adc_scan_end_isr
 .text.SystemCoreClockUpdate
                0x00001c40       0x28 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
                0x00001c40                SystemCoreClockUpdate
 .text.R_BSP_SubClockStabilizeWaitAfterReset
                0x00001c68        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
                0x00001c68                R_BSP_SubClockStabilizeWaitAfterReset
 .text.bsp_clock_init
                0x00001c6c      0x14c ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
                0x00001c6c                bsp_clock_init
 .text.R_BSP_Init_RTC
                0x00001db8       0xac ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
                0x00001db8                R_BSP_Init_RTC
 .text.bsp_init_internal
                0x00001e64        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
                0x00001e64                bsp_init_internal
                0x00001e64                bsp_init
 .text.__assert_func
                0x00001e68        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
                0x00001e68                __assert_func
 .text.bsp_prv_software_delay_loop
                0x00001e6c        0xc ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
                0x00001e6c                bsp_prv_software_delay_loop
 .text.R_BSP_SoftwareDelay
                0x00001e78       0x68 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
                0x00001e78                R_BSP_SoftwareDelay
 .text.NMI_Handler
                0x00001ee0       0x3c ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
                0x00001ee0                NMI_Handler
 .text.bsp_irq_cfg
                0x00001f1c       0xa4 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
                0x00001f1c                bsp_irq_cfg
 .text.R_BSP_RegisterProtectEnable
                0x00001fc0       0x60 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
                0x00001fc0                R_BSP_RegisterProtectEnable
 .text.R_BSP_RegisterProtectDisable
                0x00002020       0x54 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
                0x00002020                R_BSP_RegisterProtectDisable
 .text._sbrk    0x00002074       0x44 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
                0x00002074                _sbrk
 .text.Default_Handler
                0x000020b8        0x4 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
                0x000020b8                DebugMon_Handler
                0x000020b8                HardFault_Handler
                0x000020b8                PendSV_Handler
                0x000020b8                UsageFault_Handler
                0x000020b8                SecureFault_Handler
                0x000020b8                Default_Handler
                0x000020b8                MemManage_Handler
                0x000020b8                SVC_Handler
                0x000020b8                BusFault_Handler
 .text.Reset_Handler
                0x000020bc        0xc ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
                0x000020bc                Reset_Handler
 .text.SystemInit
                0x000020c8       0xe0 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
                0x000020c8                SystemInit
 .text          0x000021a8      0x378 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
                0x000021a8                __aeabi_drsub
                0x000021b0                __aeabi_dsub
                0x000021b0                __subdf3
                0x000021b4                __aeabi_dadd
                0x000021b4                __adddf3
                0x0000242c                __floatunsidf
                0x0000242c                __aeabi_ui2d
                0x0000244c                __floatsidf
                0x0000244c                __aeabi_i2d
                0x00002470                __aeabi_f2d
                0x00002470                __extendsfdf2
                0x000024b4                __floatundidf
                0x000024b4                __aeabi_ul2d
                0x000024c4                __floatdidf
                0x000024c4                __aeabi_l2d
 .text          0x00002520      0x424 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
                0x00002520                __aeabi_dmul
                0x00002520                __muldf3
                0x00002774                __divdf3
                0x00002774                __aeabi_ddiv
 .text          0x00002944      0x110 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
                0x00002944                __gtdf2
                0x00002944                __gedf2
                0x0000294c                __ltdf2
                0x0000294c                __ledf2
                0x00002954                __nedf2
                0x00002954                __eqdf2
                0x00002954                __cmpdf2
                0x000029d0                __aeabi_cdrcmple
                0x000029e0                __aeabi_cdcmpeq
                0x000029e0                __aeabi_cdcmple
                0x000029f0                __aeabi_dcmpeq
                0x00002a04                __aeabi_dcmplt
                0x00002a18                __aeabi_dcmple
                0x00002a2c                __aeabi_dcmpge
                0x00002a40                __aeabi_dcmpgt
 .text          0x00002a54       0x30 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
                0x00002a54                __aeabi_uldivmod
 .text          0x00002a84      0x2ec d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
                0x00002a84                __udivmoddi4
 .text          0x00002d70        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
                0x00002d70                __aeabi_idiv0
                0x00002d70                __aeabi_ldiv0
 .text.__errno  0x00002d74        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
                0x00002d74                __errno
 .text.memcpy   0x00002d80       0x1a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
                0x00002d80                memcpy
 .text.memset   0x00002d9a       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
                0x00002d9a                memset
 .text.__cvt    0x00002daa       0xc4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
                0x00002daa                __cvt
 .text.__exponent
                0x00002e6e       0x7c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
                0x00002e6e                __exponent
 *fill*         0x00002eea        0x2 ff
 .text._printf_float
                0x00002eec      0x46c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
                0x00002eec                _printf_float
 .text._printf_common
                0x00003358       0xe0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
                0x00003358                _printf_common
 .text._printf_i
                0x00003438      0x24c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
                0x00003438                _printf_i
 .text._scanf_float
                0x00003684      0x424 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
                0x00003684                _scanf_float
 .text.nanf     0x00003aa8        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
                0x00003aa8                nanf
 .text.sprintf  0x00003ab4       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
                0x00003ab4                siprintf
                0x00003ab4                sprintf
 .text.sscanf   0x00003af4       0x58 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
                0x00003af4                siscanf
                0x00003af4                sscanf
 .text.__seofread
                0x00003b4c        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
                0x00003b4c                __seofread
 .text          0x00003b50       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strlen.o)
                0x00003b50                strlen
 .text.strncmp  0x00003b60       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
                0x00003b60                strncmp
 .text.sulp     0x00003b88       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .text._strtod_l
                0x00003bc0      0xc04 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
                0x00003bc0                _strtod_l
 .text._strtod_r
                0x000047c4        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
                0x000047c4                _strtod_r
 .text._strtol_l.constprop.0
                0x000047d0      0x104 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .text._strtol_r
                0x000048d4        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
                0x000048d4                _strtol_r
 .text._vsnprintf_r
                0x000048d8       0x56 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
                0x000048d8                _vsniprintf_r
                0x000048d8                _vsnprintf_r
 *fill*         0x0000492e        0x2 ff
 .text.vsnprintf
                0x00004930       0x1c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
                0x00004930                vsniprintf
                0x00004930                vsnprintf
 .text.quorem   0x0000494c      0x122 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
 *fill*         0x00004a6e        0x2 ff
 .text._dtoa_r  0x00004a70      0xbd8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
                0x00004a70                _dtoa_r
 .text.rshift   0x00005648       0xa2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
 .text.__hexdig_fun
                0x000056ea       0x2a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
                0x000056ea                __hexdig_fun
 .text.__gethex
                0x00005714      0x48c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
                0x00005714                __gethex
 .text.L_shift  0x00005ba0       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
 .text.__match  0x00005bc4       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
                0x00005bc4                __match
 .text.__hexnan
                0x00005bec      0x138 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
                0x00005bec                __hexnan
 .text._localeconv_r
                0x00005d24        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
                0x00005d24                _localeconv_r
 .text.malloc   0x00005d2c       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
                0x00005d2c                malloc
 .text.__ascii_mbtowc
                0x00005d3c       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
                0x00005d3c                __ascii_mbtowc
 .text.memchr   0x00005d60       0x1c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
                0x00005d60                memchr
 .text._Balloc  0x00005d7c       0x80 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00005d7c                _Balloc
 .text._Bfree   0x00005dfc       0x44 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00005dfc                _Bfree
 .text.__multadd
                0x00005e40       0x90 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00005e40                __multadd
 .text.__s2b    0x00005ed0       0x94 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00005ed0                __s2b
 .text.__hi0bits
                0x00005f64       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00005f64                __hi0bits
 .text.__lo0bits
                0x00005fa4       0x5e d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00005fa4                __lo0bits
 *fill*         0x00006002        0x2 ff
 .text.__i2b    0x00006004       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006004                __i2b
 .text.__multiply
                0x00006030      0x150 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006030                __multiply
 .text.__pow5mult
                0x00006180       0xb4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006180                __pow5mult
 .text.__lshift
                0x00006234       0xe0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006234                __lshift
 .text.__mcmp   0x00006314       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006314                __mcmp
 .text.__mdiff  0x0000634c      0x120 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x0000634c                __mdiff
 .text.__ulp    0x0000646c       0x54 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x0000646c                __ulp
 .text.__b2d    0x000064c0       0xa0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x000064c0                __b2d
 .text.__d2b    0x00006560       0xb8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006560                __d2b
 .text.__ratio  0x00006618       0x66 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00006618                __ratio
 .text.__copybits
                0x0000667e       0x46 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x0000667e                __copybits
 .text.__any_on
                0x000066c4       0x42 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x000066c4                __any_on
 .text._calloc_r
                0x00006706       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
                0x00006706                _calloc_r
 *fill*         0x00006732        0x2 ff
 .text._free_r  0x00006734       0x98 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
                0x00006734                _free_r
 .text.sbrk_aligned
                0x000067cc       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
 .text._malloc_r
                0x0000680c       0xe8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
                0x0000680c                _malloc_r
 .text.__ssputs_r
                0x000068f4       0xb6 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
                0x000068f4                __ssputs_r
 *fill*         0x000069aa        0x2 ff
 .text._svfprintf_r
                0x000069ac      0x200 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
                0x000069ac                _svfiprintf_r
                0x000069ac                _svfprintf_r
 .text._sungetc_r
                0x00006bac       0x7a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
                0x00006bac                _sungetc_r
 .text.__ssrefill_r
                0x00006c26       0x3a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
                0x00006c26                __ssrefill_r
 .text.__ssvfscanf_r
                0x00006c60      0x2f8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
                0x00006c60                __ssvfiscanf_r
                0x00006c60                __ssvfscanf_r
 .text._scanf_chars
                0x00006f58       0xb4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
                0x00006f58                _scanf_chars
 .text._scanf_i
                0x0000700c      0x1f0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
                0x0000700c                _scanf_i
 *fill*         0x000071fc        0x4 ff
 .text.nan      0x00007200       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
                0x00007200                nan
 .text._sbrk_r  0x00007210       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
                0x00007210                _sbrk_r
 .text.__sccl   0x00007230       0x70 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
                0x00007230                __sccl
 .text._strtoul_l.constprop.0
                0x000072a0       0xe4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .text._strtoul_r
                0x00007384        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
                0x00007384                _strtoul_r
 .text.__submore
                0x00007388       0x6e d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
                0x00007388                __submore
 .text.__ascii_wctomb
                0x000073f6       0x1a d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
                0x000073f6                __ascii_wctomb
 .text.__retarget_lock_acquire_recursive
                0x00007410        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
                0x00007410                __retarget_lock_acquire_recursive
 .text.__retarget_lock_release_recursive
                0x00007412        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
                0x00007412                __retarget_lock_release_recursive
 .text.memmove  0x00007414       0x34 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
                0x00007414                memmove
 .text.__malloc_lock
                0x00007448        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
                0x00007448                __malloc_lock
 .text.__malloc_unlock
                0x00007454        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
                0x00007454                __malloc_unlock
 .text._realloc_r
                0x00007460       0x5e d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
                0x00007460                _realloc_r
 .text._malloc_usable_size_r
                0x000074be       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
                0x000074be                _malloc_usable_size_r
 *fill*         0x000074ce        0x2 ff
 .text          0x000074d0       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
                0x000074d0                __unorddf2
                0x000074d0                __aeabi_dcmpun
 .text          0x000074fc       0x50 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
                0x000074fc                __aeabi_d2iz
                0x000074fc                __fixdfsi
 .text          0x0000754c       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
                0x0000754c                __aeabi_d2uiz
                0x0000754c                __fixunsdfsi
 .text          0x0000758c       0xa0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
                0x0000758c                __truncdfsf2
                0x0000758c                __aeabi_d2f
 .text          0x0000762c       0x30 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
                0x0000762c                __fixdfdi
                0x0000762c                __aeabi_d2lz
 .text          0x0000765c       0x3c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
                0x0000765c                __fixunsdfdi
                0x0000765c                __aeabi_d2ulz
 *(.version)
 .version       0x00007698       0x50 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 *(.init)
 .init          0x000076e8        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
                0x000076e8                _init
 .init          0x000076ec        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o
 *(.fini)
 .fini          0x000076f4        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
                0x000076f4                _fini
 .fini          0x000076f8        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o
 *crtbegin.o(.ctors)
 *crtbegin?.o(.ctors)
 *(EXCLUDE_FILE(*crtend.o *crtend?.o) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
 *crtbegin.o(.dtors)
 *crtbegin?.o(.dtors)
 *(EXCLUDE_FILE(*crtend.o *crtend?.o) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
 *(.rodata*)
 .rodata.SysTick_Init.str1.4
                0x00007700       0x25 ./src/systick/systick.o
 *fill*         0x00007725        0x3 ff
 .rodata.__func__.0
                0x00007728        0xd ./src/systick/systick.o
 *fill*         0x00007735        0x3 ff
 .rodata.debug_uart4_Init.str1.4
                0x00007738       0x37 ./src/app/debug_uart/debug_uart.o
 *fill*         0x0000776f        0x1 ff
 .rodata.__func__.0
                0x00007770       0x11 ./src/app/debug_uart/debug_uart.o
 *fill*         0x00007781        0x3 ff
 .rodata.adc_init.str1.4
                0x00007784       0x19 ./src/app/adc/adc_app.o
                                 0x2d (size before relaxing)
 *fill*         0x0000779d        0x3 ff
 .rodata.__func__.0
                0x000077a0        0x9 ./src/app/adc/adc_app.o
 *fill*         0x000077a9        0x3 ff
 .rodata.parse_uart4_command.part.0.str1.4
                0x000077ac       0x1d ./src/task.o
 *fill*         0x000077c9        0x3 ff
 .rodata.adc_task.str1.4
                0x000077cc        0xe ./src/task.o
 *fill*         0x000077da        0x2 ff
 .rodata.debug_uart4_task.str1.4
                0x000077dc       0x61 ./src/task.o
 *fill*         0x0000783d        0x3 ff
 .rodata.g_ioport
                0x00007840        0xc ./ra_gen/common_data.o
                0x00007840                g_ioport
 .rodata.g_adc0_cfg
                0x0000784c       0x18 ./ra_gen/hal_data.o
                0x0000784c                g_adc0_cfg
 .rodata.g_adc0_cfg_extend
                0x00007864        0xb ./ra_gen/hal_data.o
                0x00007864                g_adc0_cfg_extend
 *fill*         0x0000786f        0x1 ff
 .rodata.g_adc0_channel_cfg
                0x00007870       0x14 ./ra_gen/hal_data.o
                0x00007870                g_adc0_channel_cfg
 .rodata.g_timer0_cfg
                0x00007884       0x20 ./ra_gen/hal_data.o
                0x00007884                g_timer0_cfg
 .rodata.g_timer0_extend
                0x000078a4       0x3c ./ra_gen/hal_data.o
                0x000078a4                g_timer0_extend
 .rodata.g_timer6_cfg
                0x000078e0       0x20 ./ra_gen/hal_data.o
                0x000078e0                g_timer6_cfg
 .rodata.g_timer6_extend
                0x00007900       0x3c ./ra_gen/hal_data.o
                0x00007900                g_timer6_extend
 .rodata.g_uart4_cfg
                0x0000793c       0x20 ./ra_gen/hal_data.o
                0x0000793c                g_uart4_cfg
 .rodata.g_uart4_cfg_extend
                0x0000795c       0x14 ./ra_gen/hal_data.o
                0x0000795c                g_uart4_cfg_extend
 .rodata.g_bsp_pin_cfg
                0x00007970        0xc ./ra_gen/pin_data.o
                0x00007970                g_bsp_pin_cfg
 .rodata.g_bsp_pin_cfg_data
                0x0000797c       0x60 ./ra_gen/pin_data.o
                0x0000797c                g_bsp_pin_cfg_data
 .rodata.g_interrupt_event_link_select
                0x000079dc       0xc0 ./ra_gen/vector_data.o
                0x000079dc                g_interrupt_event_link_select
 .rodata.g_ioport_on_ioport
                0x00007a9c       0x34 ./ra/fsp/src/r_ioport/r_ioport.o
                0x00007a9c                g_ioport_on_ioport
 .rodata.g_vbatt_pins_input
                0x00007ad0        0x6 ./ra/fsp/src/r_ioport/r_ioport.o
 *fill*         0x00007ad6        0x2 ff
 .rodata.g_prcr_masks
                0x00007ad8        0x8 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .rodata._printf_float.str1.1
                0x00007ae0       0x10 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
                                 0x12 (size before relaxing)
 .rodata._printf_i.str1.1
                0x00007af0       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .rodata._scanf_float.str1.1
                0x00007b12        0x5 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
                                  0x6 (size before relaxing)
 .rodata._strtod_l.str1.1
                0x00007b17        0x5 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
                                  0xd (size before relaxing)
 *fill*         0x00007b17        0x1 ff
 .rodata.fpi.1  0x00007b18       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .rodata.fpinan.0
                0x00007b2c       0x14 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .rodata.tinytens
                0x00007b40       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .rodata._ctype_
                0x00007b68      0x101 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ctype_.o)
                0x00007b68                _ctype_
 .rodata._dtoa_r.str1.1
                0x00007c69       0xb3 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
                                 0xb5 (size before relaxing)
 .rodata.__gethex.str1.1
                0x00007d1c       0x86 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
                                 0x97 (size before relaxing)
 .rodata.str1.1
                0x00007da2        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
                                  0x3 (size before relaxing)
 .rodata._C_numeric_locale
                0x00007da4        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
                0x00007da4                _C_numeric_locale
 .rodata._setlocale_r.str1.1
                0x00007db0        0x8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
                                  0x9 (size before relaxing)
 .rodata.str1.1
                0x00007db8        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .rodata._Balloc.str1.1
                0x00007db8       0x7f d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                                 0x96 (size before relaxing)
 .rodata.__multadd.str1.1
                0x00007e37       0x11 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 *fill*         0x00007e37        0x1 ff
 .rodata.__mprec_bigtens
                0x00007e38       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00007e38                __mprec_bigtens
 .rodata.__mprec_tens
                0x00007e60       0xc8 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
                0x00007e60                __mprec_tens
 .rodata.p05.0  0x00007f28        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .rodata._svfprintf_r.str1.1
                0x00007f34       0x11 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .rodata.__ssvfscanf_r.str1.1
                0x00007f45        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
 .rodata.str1.1
                0x00007f45        0x9 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .rodata._scanf_i.str1.1
                0x00007f4e       0x12 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .rodata        0x00007f60        0xc d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .rodata.__sf_fake_stderr
                0x00007f6c       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
                0x00007f6c                __sf_fake_stderr
 .rodata.__sf_fake_stdin
                0x00007f8c       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
                0x00007f8c                __sf_fake_stdin
 .rodata.__sf_fake_stdout
                0x00007fac       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
                0x00007fac                __sf_fake_stdout
                0x00007fcc                __usb_dev_descriptor_start_fs = .
 *(.usb_device_desc_fs*)
                0x00007fcc                __usb_cfg_descriptor_start_fs = .
 *(.usb_config_desc_fs*)
                0x00007fcc                __usb_interface_descriptor_start_fs = .
 *(.usb_interface_desc_fs*)
                0x00007fcc                __usb_descriptor_end_fs = .
                0x00007fcc                __usb_dev_descriptor_start_hs = .
 *(.usb_device_desc_hs*)
                0x00007fcc                __usb_cfg_descriptor_start_hs = .
 *(.usb_config_desc_hs*)
                0x00007fcc                __usb_interface_descriptor_start_hs = .
 *(.usb_interface_desc_hs*)
                0x00007fcc                __usb_descriptor_end_hs = .
 *(.eh_frame*)
 .eh_frame      0x00007fcc        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
 .eh_frame      0x00007fcc        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
                0x00007fd0                __ROM_End = .
                0x000001c0                __Vectors_Size = (__Vectors_End - __Vectors)

.glue_7         0x00007fd0        0x0
 .glue_7        0x00007fd0        0x0 linker stubs

.glue_7t        0x00007fd0        0x0
 .glue_7t       0x00007fd0        0x0 linker stubs

.vfp11_veneer   0x00007fd0        0x0
 .vfp11_veneer  0x00007fd0        0x0 linker stubs

.v4_bx          0x00007fd0        0x0
 .v4_bx         0x00007fd0        0x0 linker stubs

.iplt           0x00007fd0        0x0
 .iplt          0x00007fd0        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
                0x00007fd0                . = .
                0x00007fd0                __itcm_data_pre_location = .

.itcm_data      0x00000000        0x0 load address 0x00007fd0
                0x00000000                __tz_ITCM_S = ABSOLUTE (ITCM_START)
                0x00000000                __itcm_data_start = .
 *(.itcm_data*)
                0x00000000                . = ALIGN (0x8)
                0x00000000                __itcm_data_end = .
                0x00000000                __tz_ITCM_N = DEFINED (ITCM_NS_START)?ABSOLUTE (ITCM_NS_START):ALIGN (__itcm_data_end, 0x2000)
                0x00007fd0                __itcm_data_init_start = LOADADDR (.itcm_data)
                0x00007fd0                __itcm_data_init_end = (LOADADDR (.itcm_data) + SIZEOF (.itcm_data))
                0x00000001                ASSERT (((ORIGIN (ITCM) % 0x8) == 0x0), ITCM memory region origin must be aligned to 8 bytes.)
                0x00000001                ASSERT (((LENGTH (ITCM) % 0x8) == 0x0), ITCM memory region length must be a multiple of 8 bytes.)
                0x00000001                ASSERT (((LOADADDR (.itcm_data) % 0x10) == 0x0), .itcm_data section must be aligned to 16 bytes.)
                0x00000001                ASSERT (((SIZEOF (.itcm_data) % 0x8) == 0x0), .itcm_data section size must be a multiple of 8 bytes.)
                0x00007fd0                . = (SIZEOF (.itcm_data) > 0x0)?__itcm_data_init_end:__itcm_data_pre_location
                0x00007fd0                __exidx_start = .

/DISCARD/
 *(.ARM.extab* .gnu.linkonce.armextab.*)
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
                0x00007fd0                __exidx_end = .
                0x00007fd0                __etext = .
                0x20000000                __tz_RAM_S = ORIGIN (RAM)

.fsp_dtc_vector_table
                0x20000000        0x0
                0x20000000                . = ORIGIN (RAM)
 *(.fsp_dtc_vector_table)

.data           0x20000000      0x210 load address 0x00007fd0
                0x20000000                __data_start__ = .
                0x20000000                . = ALIGN (0x4)
                0x20000000                __Code_In_RAM_Start = .
 *(.code_in_ram*)
                0x20000000                __Code_In_RAM_End = .
 *(vtable)
 *(.data.*)
 .data.scheduler_task
                0x20000000       0x30 ./src/scheduler/scheduler.o
 .data.g_uart4_baud_setting
                0x20000030        0x4 ./ra_gen/hal_data.o
                0x20000030                g_uart4_baud_setting
 .data._impure_ptr
                0x20000034        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
                0x20000034                _impure_ptr
 .data.impure_data
                0x20000038       0x60 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
 .data.numempty
                0x20000098        0x2 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 *fill*         0x2000009a        0x2 
 .data.__global_locale
                0x2000009c      0x16c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
                0x2000009c                __global_locale
 *(.data)
                0x20000208                . = ALIGN (0x4)
                0x20000208                PROVIDE (__preinit_array_start = .)
 *(.preinit_array)
                0x20000208                PROVIDE (__preinit_array_end = .)
                0x20000208                . = ALIGN (0x4)
                0x20000208                PROVIDE (__init_array_start = .)
 *(SORT_BY_NAME(.init_array.*))
 *(.init_array)
 .init_array    0x20000208        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
                0x2000020c                PROVIDE (__init_array_end = .)
                0x2000020c                . = ALIGN (0x4)
                [!provide]                PROVIDE (__fini_array_start = .)
 *(SORT_BY_NAME(.fini_array.*))
 *(.fini_array)
 .fini_array    0x2000020c        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
                [!provide]                PROVIDE (__fini_array_end = .)
 *(.jcr*)
                0x20000210                . = ALIGN (0x4)
                0x20000210                __data_end__ = .

.igot.plt       0x20000210        0x0 load address 0x000081e0
 .igot.plt      0x20000210        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
                0x20000210                . = .
                0x000081e0                __dtcm_data_pre_location = (LOADADDR (.data) + SIZEOF (.data))

.dtcm_data      0x00000000        0x0 load address 0x000081e0
                0x00000000                __tz_DTCM_S = ABSOLUTE (DTCM_START)
                0x00000000                __dtcm_data_start = .
 *(.dtcm_data*)
                0x00000000                . = ALIGN (0x8)
                0x00000000                __dtcm_data_end = .
                0x00000000                . = __dtcm_data_end

.dtcm_bss       0x00000000        0x0
                0x00000000                __dtcm_bss_start = .
 *(.dtcm_bss*)
                0x00000000                . = ALIGN (0x8)
                0x00000000                __dtcm_bss_end = .
                0x00000000                __tz_DTCM_N = DEFINED (DTCM_NS_START)?ABSOLUTE (DTCM_NS_START):ALIGN (__dtcm_bss_end, 0x2000)
                0x000081e0                __dtcm_data_init_start = LOADADDR (.dtcm_data)
                0x000081e0                __dtcm_data_init_end = (LOADADDR (.dtcm_data) + SIZEOF (.dtcm_data))
                0x00000001                ASSERT (((ORIGIN (DTCM) % 0x8) == 0x0), DTCM memory region origin must be aligned to 8 bytes.)
                0x00000001                ASSERT (((LENGTH (DTCM) % 0x8) == 0x0), DTCM memory region length must be a multiple of 8 bytes.)
                0x00000001                ASSERT ((LOADADDR (.dtcm_bss) == ADDR (.dtcm_bss)), .dtcm_bss has (VMA != LMA) but should be NOLOAD (VMA == LMA).)
                0x00000001                ASSERT (((LOADADDR (.dtcm_data) % 0x10) == 0x0), .dtcm_data section must be aligned to 16 bytes.)
                0x00000001                ASSERT (((SIZEOF (.dtcm_data) % 0x8) == 0x0), .dtcm_data section size must be a multiple of 8 bytes.)
                0x00000001                ASSERT (((LOADADDR (.dtcm_bss) % 0x8) == 0x0), .dtcm_bss section must be aligned to 8 bytes.)
                0x00000001                ASSERT (((SIZEOF (.dtcm_bss) % 0x8) == 0x0), .dtcm_bss section size must be a multiple of 8 bytes.)
                0x00000001                ASSERT ((__dtcm_bss_start == __dtcm_data_end), .dtcm_bss section is not adjacent to .dtcm_data section.)
                0x000081e0                . = (SIZEOF (.dtcm_data) > 0x0)?__dtcm_data_init_end:__dtcm_data_pre_location
                0x000081e0                sgstubs_pre_location = .
                0x00008400                SGSTUBS_LOC = (DEFINED (PROJECT_SECURE) && DEFINED (FLASH_NSC_START))?ABSOLUTE (FLASH_NSC_START):ALIGN (0x400)

.gnu.sgstubs    0x00008400        0x0
                0x00008400                __tz_FLASH_C = DEFINED (FLASH_NSC_START)?ABSOLUTE (FLASH_NSC_START):__RESERVE_NS_RAM?ABSOLUTE ((FLASH_START + FLASH_LENGTH)):ALIGN (0x400)
                0x00008400                _start_sg = .
 *(.gnu.sgstubs*)
                0x00008400                . = ALIGN (0x20)
                0x00008400                _end_sg = .
                0x00010000                __tz_FLASH_N = DEFINED (FLASH_NS_START)?ABSOLUTE (FLASH_NS_START):__RESERVE_NS_RAM?ABSOLUTE ((FLASH_START + FLASH_LENGTH)):(FLASH_LENGTH < 0x8000)?FLASH_LENGTH:ALIGN (0x8000)
                0x00010000                FLASH_NS_IMAGE_START = DEFINED (FLASH_NS_IMAGE_START)?FLASH_NS_IMAGE_START:__tz_FLASH_N
                0x60000000                __tz_QSPI_FLASH_S = ORIGIN (QSPI_FLASH)

.qspi_flash     0x60000000        0x0
                0x60000000                __qspi_flash_start__ = .
 *(.qspi_flash*)
 *(.code_in_qspi*)
                0x60000000                __qspi_flash_end__ = .
                0x00000000                __qspi_flash_code_size__ = (__qspi_flash_end__ - __qspi_flash_start__)
                0x000081e0                __qspi_flash_code_addr__ = sgstubs_pre_location

.qspi_non_retentive
                0x60000000        0x0 load address 0x000081e0
                0x60000000                __qspi_non_retentive_start__ = .
 *(.qspi_non_retentive*)
                0x60000000                __qspi_non_retentive_end__ = .
                0x00000000                __qspi_non_retentive_size__ = (__qspi_non_retentive_end__ - __qspi_non_retentive_start__)
                0x04000000                __qspi_region_max_size__ = 0x4000000
                0x60000000                __qspi_region_start_address__ = __qspi_flash_start__
                0x64000000                __qspi_region_end_address__ = (__qspi_flash_start__ + __qspi_region_max_size__)
                0x60000000                __tz_QSPI_FLASH_N = __qspi_non_retentive_end__

.OSPI_DEVICE_0_NO_LOAD
                0x68000000        0x0
                0x68000000                . = ALIGN (0x4)
                0x68000000                __ospi_device_0_start__ = .
 *(.ospi_device_0_no_load*)
                0x68000000                . = ALIGN (0x4)
                0x68000000                __ospi_device_0_end__ = .

.OSPI_DEVICE_1_NO_LOAD
                0x70000000        0x0
                0x70000000                . = ALIGN (0x4)
                0x70000000                __ospi_device_1_start__ = .
 *(.ospi_device_1_no_load*)
                0x70000000                . = ALIGN (0x4)
                0x70000000                __ospi_device_1_end__ = .
                0x68000000                __tz_OSPI_DEVICE_0_S = ORIGIN (OSPI_DEVICE_0)

.OSPI_DEVICE_0  0x68000000        0x0
                0x68000000                __ospi_device_0_start__ = .
 *(.ospi_device_0*)
 *(.code_in_ospi_device_0*)
                0x68000000                __ospi_device_0_end__ = .
                0x00000000                __ospi_device_0_code_size__ = (__ospi_device_0_end__ - __ospi_device_0_start__)
                0x000081e0                __ospi_device_0_code_addr__ = (sgstubs_pre_location + SIZEOF (.qspi_non_retentive))

.ospi_device_0_non_retentive
                0x68000000        0x0 load address 0x000081e0
                0x68000000                __ospi_device_0_non_retentive_start__ = .
 *(.ospi_device_0_non_retentive*)
                0x68000000                __ospi_device_0_non_retentive_end__ = .
                0x00000000                __ospi_device_0_non_retentive_size__ = (__ospi_device_0_non_retentive_end__ - __ospi_device_0_non_retentive_start__)
                0x08000000                __ospi_device_0_region_max_size__ = 0x8000000
                0x68000000                __ospi_device_0_region_start_address__ = __ospi_device_0_start__
                0x70000000                __ospi_device_0_region_end_address__ = (__ospi_device_0_start__ + __ospi_device_0_region_max_size__)
                0x68000000                __tz_OSPI_DEVICE_0_N = __ospi_device_0_non_retentive_end__
                0x70000000                __tz_OSPI_DEVICE_1_S = ORIGIN (OSPI_DEVICE_1)

.OSPI_DEVICE_1  0x70000000        0x0
                0x70000000                __ospi_device_1_start__ = .
 *(.ospi_device_1*)
 *(.code_in_ospi_device_1*)
                0x70000000                __ospi_device_1_end__ = .
                0x00000000                __ospi_device_1_code_size__ = (__ospi_device_1_end__ - __ospi_device_1_start__)
                0x000081e0                __ospi_device_1_code_addr__ = (sgstubs_pre_location + (SIZEOF (.qspi_non_retentive) + SIZEOF (.ospi_device_0_non_retentive)))

.ospi_device_1_non_retentive
                0x70000000        0x0 load address 0x000081e0
                0x70000000                __ospi_device_1_non_retentive_start__ = .
 *(.ospi_device_1_non_retentive*)
                0x70000000                __ospi_device_1_non_retentive_end__ = .
                0x00000000                __ospi_device_1_non_retentive_size__ = (__ospi_device_1_non_retentive_end__ - __ospi_device_1_non_retentive_start__)
                0x10000000                __ospi_device_1_region_max_size__ = 0x10000000
                0x70000000                __ospi_device_1_region_start_address__ = __ospi_device_1_start__
                0x80000000                __ospi_device_1_region_end_address__ = (__ospi_device_1_start__ + __ospi_device_1_region_max_size__)
                0x70000000                __tz_OSPI_DEVICE_1_N = __ospi_device_1_non_retentive_end__

.noinit         0x20000210       0x20
                0x20000210                . = ALIGN (0x4)
                0x20000210                __noinit_start = .
 *(.noinit*)
 .noinit        0x20000210       0x1c ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
                0x20000230                . = ALIGN (0x8)
 *fill*         0x2000022c        0x4 
 *(.heap.*)
                0x20000230                __noinit_end = .

.bss            0x20000230      0x51c
                0x20000230                . = ALIGN (0x4)
                0x20000230                __bss_start__ = .
 *(.bss*)
 .bss           0x20000230       0x1c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
 .bss.IT_Period
                0x2000024c        0x4 ./src/systick/systick.o
 .bss.IT_nums   0x20000250        0x4 ./src/systick/systick.o
 .bss.uwTick    0x20000254        0x4 ./src/systick/systick.o
                0x20000254                uwTick
 .bss.task_num  0x20000258        0x4 ./src/scheduler/scheduler.o
                0x20000258                task_num
 .bss.Circular_queue_uart4
                0x2000025c      0x130 ./src/circular_queue/circular_queue.o
                0x2000025c                Circular_queue_uart4
 .bss.key_down  0x2000038c        0x1 ./src/app/key/key_app.o
                0x2000038c                key_down
 .bss.key_old   0x2000038d        0x1 ./src/app/key/key_app.o
                0x2000038d                key_old
 .bss.key_up    0x2000038e        0x1 ./src/app/key/key_app.o
                0x2000038e                key_up
 .bss.key_val   0x2000038f        0x1 ./src/app/key/key_app.o
                0x2000038f                key_val
 .bss.sw2_long_flag
                0x20000390        0x1 ./src/app/key/key_app.o
                0x20000390                sw2_long_flag
 *fill*         0x20000391        0x1 
 .bss.sw2_time  0x20000392        0x2 ./src/app/key/key_app.o
                0x20000392                sw2_time
 .bss.sw3_long_flag
                0x20000394        0x1 ./src/app/key/key_app.o
                0x20000394                sw3_long_flag
 *fill*         0x20000395        0x1 
 .bss.sw3_time  0x20000396        0x2 ./src/app/key/key_app.o
                0x20000396                sw3_time
 .bss.uart0_send_complete_flag
                0x20000398        0x1 ./src/app/debug_uart/debug_uart.o
                0x20000398                uart0_send_complete_flag
 *fill*         0x20000399        0x3 
 .bss.uart4_Read_Buffer
                0x2000039c      0x12c ./src/app/debug_uart/debug_uart.o
                0x2000039c                uart4_Read_Buffer
 .bss.uart4_Read_Length
                0x200004c8        0x2 ./src/app/debug_uart/debug_uart.o
                0x200004c8                uart4_Read_Length
 .bss.scan_complete_flag
                0x200004ca        0x1 ./src/app/adc/adc_app.o
                0x200004ca                scan_complete_flag
 *fill*         0x200004cb        0x1 
 .bss.g_ioport_ctrl
                0x200004cc        0x8 ./ra_gen/common_data.o
                0x200004cc                g_ioport_ctrl
 .bss.g_adc0_ctrl
                0x200004d4       0x24 ./ra_gen/hal_data.o
                0x200004d4                g_adc0_ctrl
 .bss.g_timer0_ctrl
                0x200004f8       0x20 ./ra_gen/hal_data.o
                0x200004f8                g_timer0_ctrl
 .bss.g_timer6_ctrl
                0x20000518       0x20 ./ra_gen/hal_data.o
                0x20000518                g_timer6_ctrl
 .bss.g_uart4_ctrl
                0x20000538       0x30 ./ra_gen/hal_data.o
                0x20000538                g_uart4_ctrl
 .bss.g_bsp_group_irq_sources
                0x20000568       0x40 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
                0x20000568                g_bsp_group_irq_sources
 .bss.g_protect_pfswe_counter
                0x200005a8        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
                0x200005a8                g_protect_pfswe_counter
 .bss.gp_renesas_isr_context
                0x200005ac      0x180 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
                0x200005ac                gp_renesas_isr_context
 .bss.g_protect_counters
                0x2000072c        0x8 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
                0x2000072c                g_protect_counters
 .bss.current_heap_end.0
                0x20000734        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .bss.SystemCoreClock
                0x20000738        0x4 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
                0x20000738                SystemCoreClock
 .bss.__malloc_free_list
                0x2000073c        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
                0x2000073c                __malloc_free_list
 .bss.__malloc_sbrk_start
                0x20000740        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
                0x20000740                __malloc_sbrk_start
 .bss.errno     0x20000744        0x4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
                0x20000744                errno
 .bss.__lock___malloc_recursive_mutex
                0x20000748        0x1 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
                0x20000748                __lock___malloc_recursive_mutex
 *(COMMON)
                0x2000074c                . = ALIGN (0x4)
 *fill*         0x20000749        0x3 
                0x2000074c                __bss_end__ = .

.heap           0x20000750     0x1000
                0x20000750                . = ALIGN (0x8)
                0x20000750                __HeapBase = .
 *(.heap)
 .heap          0x20000750     0x1000 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
                0x20001750                __HeapLimit = .

.stack_dummy    0x20001750      0x800
                0x20001750                . = ALIGN (0x8)
                0x20001750                __StackLimit = .
 *(.stack)
 .stack         0x20001750      0x800 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
                0x20001f50                __StackTop = .
 *(.stack*)
                0x20001f50                __StackTopAll = .
                0x20001f50                PROVIDE (__stack = __StackTopAll)
                0x20001f50                __RAM_segment_used_end__ = ALIGN (__StackTopAll, 0x4)
                0x20002000                __tz_RAM_C = DEFINED (RAM_NSC_START)?ABSOLUTE ((RAM_NSC_START - RAM_NS_BUFFER_BLOCK_LENGTH)):__RESERVE_NS_RAM?ABSOLUTE (RAM_NS_BUFFER_BLOCK_START):ALIGN (__RAM_segment_used_end__, 0x400)
                0x20002000                __tz_RAM_N = DEFINED (RAM_NS_START)?ABSOLUTE ((RAM_NS_START - RAM_NS_BUFFER_BLOCK_LENGTH)):__RESERVE_NS_RAM?ABSOLUTE (RAM_NS_BUFFER_BLOCK_START):ALIGN (__tz_RAM_C, 0x2000)

.ns_buffer      0x20001f50        0x0
                0x20001f50                . = __RESERVE_NS_RAM?ABSOLUTE ((RAM_NS_BUFFER_START & 0xffffffe0)):.
 *(.ns_buffer*)

.data_flash     0x08000000        0x0
                0x08000000                . = ORIGIN (DATA_FLASH)
                0x08000000                __tz_DATA_FLASH_S = .
                0x08000000                __Data_Flash_Start = .
 *(.data_flash*)
                0x08000000                __Data_Flash_End = .
                0x08000000                __tz_DATA_FLASH_N = DEFINED (DATA_FLASH_NS_START)?ABSOLUTE (DATA_FLASH_NS_START):__RESERVE_NS_RAM?ABSOLUTE ((DATA_FLASH_START + DATA_FLASH_LENGTH)):ALIGN (0x400)
                0x80010000                __tz_SDRAM_S = ORIGIN (SDRAM)

.sdram          0x80010000        0x0
                0x80010000                __SDRAM_Start = .
 *(.sdram*)
 *(.frame*)
                0x80010000                __SDRAM_End = .
                0x80010000                __tz_SDRAM_N = __SDRAM_End
                0x00000000                __tz_ID_CODE_S = ORIGIN (ID_CODE)
                0x00000000                __tz_ID_CODE_N = __tz_ID_CODE_S

.id_code        0x00000000        0x0
                0x00000000                __ID_Code_Start = .
 *(.id_code*)
                0x00000000                __ID_Code_End = .
                0x0100a100                __tz_OPTION_SETTING_S = ORIGIN (OPTION_SETTING_OFS)

.option_setting_ofs
                0x0100a100       0x14
                0x0100a100                __OPTION_SETTING_OFS_Start = .
 *(.option_setting_ofs0)
 .option_setting_ofs0
                0x0100a100        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a104                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_OFS_Start + 0x4):__OPTION_SETTING_OFS_Start
 *(.option_setting_ofs2)
                0x0100a110                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_OFS_Start + 0x10):__OPTION_SETTING_OFS_Start
 *fill*         0x0100a104        0xc ff
 *(.option_setting_dualsel)
 .option_setting_dualsel
                0x0100a110        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a114                __OPTION_SETTING_OFS_End = .

.option_setting_sas
                0x0100a134        0x4
                0x0100a134                __OPTION_SETTING_SAS_Start = .
 *(.option_setting_sas)
 .option_setting_sas
                0x0100a134        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a138                __OPTION_SETTING_SAS_End = .
                0x0100a180                __tz_OPTION_SETTING_N = ABSOLUTE (OPTION_SETTING_START_NS)

.option_setting_ns
                0x0100a100        0x0
                0x0100a100                __OPTION_SETTING_NS_Start = .
 *(.option_setting_ofs1)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x4):__OPTION_SETTING_NS_Start
 *(.option_setting_ofs3)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x10):__OPTION_SETTING_NS_Start
 *(.option_setting_banksel)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x40):__OPTION_SETTING_NS_Start
 *(.option_setting_bps0)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x44):__OPTION_SETTING_NS_Start
 *(.option_setting_bps1)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x48):__OPTION_SETTING_NS_Start
 *(.option_setting_bps2)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x4c):__OPTION_SETTING_NS_Start
 *(.option_setting_bps3)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x60):__OPTION_SETTING_NS_Start
 *(.option_setting_pbps0)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x64):__OPTION_SETTING_NS_Start
 *(.option_setting_pbps1)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x68):__OPTION_SETTING_NS_Start
 *(.option_setting_pbps2)
                0x0100a100                . = USE_OPTION_SETTING_NS?(__OPTION_SETTING_NS_Start + 0x6c):__OPTION_SETTING_NS_Start
 *(.option_setting_pbps3)
                0x0100a100                __OPTION_SETTING_NS_End = .
                0x0100a200                __tz_OPTION_SETTING_S_S = ORIGIN (OPTION_SETTING_S)

.option_setting_s
                0x0100a200       0xd0
                0x0100a200                __OPTION_SETTING_S_Start = .
 *(.option_setting_ofs1_sec)
 .option_setting_ofs1_sec
                0x0100a200        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a204                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x4):__OPTION_SETTING_S_Start
 *(.option_setting_ofs3_sec)
                0x0100a210                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x10):__OPTION_SETTING_S_Start
 *fill*         0x0100a204        0xc ff
 *(.option_setting_banksel_sec)
 .option_setting_banksel_sec
                0x0100a210        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a240                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x40):__OPTION_SETTING_S_Start
 *fill*         0x0100a214       0x2c ff
 *(.option_setting_bps_sec0)
 .option_setting_bps_sec0
                0x0100a240        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a244                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x44):__OPTION_SETTING_S_Start
 *(.option_setting_bps_sec1)
 .option_setting_bps_sec1
                0x0100a244        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a248                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x48):__OPTION_SETTING_S_Start
 *(.option_setting_bps_sec2)
 .option_setting_bps_sec2
                0x0100a248        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a24c                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x4c):__OPTION_SETTING_S_Start
 *(.option_setting_bps_sec3)
 .option_setting_bps_sec3
                0x0100a24c        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a260                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x60):__OPTION_SETTING_S_Start
 *fill*         0x0100a250       0x10 ff
 *(.option_setting_pbps_sec0)
 .option_setting_pbps_sec0
                0x0100a260        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a264                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x64):__OPTION_SETTING_S_Start
 *(.option_setting_pbps_sec1)
 .option_setting_pbps_sec1
                0x0100a264        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a268                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x68):__OPTION_SETTING_S_Start
 *(.option_setting_pbps_sec2)
 .option_setting_pbps_sec2
                0x0100a268        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a26c                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x6c):__OPTION_SETTING_S_Start
 *(.option_setting_pbps_sec3)
 .option_setting_pbps_sec3
                0x0100a26c        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a280                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x80):__OPTION_SETTING_S_Start
 *fill*         0x0100a270       0x10 ff
 *(.option_setting_ofs1_sel)
 .option_setting_ofs1_sel
                0x0100a280        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a284                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x84):__OPTION_SETTING_S_Start
 *(.option_setting_ofs3_sel)
                0x0100a290                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0x90):__OPTION_SETTING_S_Start
 *fill*         0x0100a284        0xc ff
 *(.option_setting_banksel_sel)
 .option_setting_banksel_sel
                0x0100a290        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a2c0                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0xc0):__OPTION_SETTING_S_Start
 *fill*         0x0100a294       0x2c ff
 *(.option_setting_bps_sel0)
 .option_setting_bps_sel0
                0x0100a2c0        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a2c4                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0xc4):__OPTION_SETTING_S_Start
 *(.option_setting_bps_sel1)
 .option_setting_bps_sel1
                0x0100a2c4        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a2c8                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0xc8):__OPTION_SETTING_S_Start
 *(.option_setting_bps_sel2)
 .option_setting_bps_sel2
                0x0100a2c8        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a2cc                . = PROJECT_SECURE_OR_FLAT?(__OPTION_SETTING_S_Start + 0xcc):__OPTION_SETTING_S_Start
 *(.option_setting_bps_sel3)
 .option_setting_bps_sel3
                0x0100a2cc        0x4 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                0x0100a2d0                __OPTION_SETTING_S_End = .
                0x0100a2d0                __tz_OPTION_SETTING_S_N = __OPTION_SETTING_S_End
OUTPUT(Ability_Assessment_Project.elf elf32-littlearm)
LOAD linker stubs

.rel.dyn        0x0100a2d0        0x0
 .rel.iplt      0x0100a2d0        0x0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o

.ARM.attributes
                0x00000000       0x36
 .ARM.attributes
                0x00000000       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crti.o
 .ARM.attributes
                0x00000022       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtbegin.o
 .ARM.attributes
                0x0000005a       0x3a ./src/time/gpt0_timing.o
 .ARM.attributes
                0x00000094       0x3a ./src/time/gpt6_pwm.o
 .ARM.attributes
                0x000000ce       0x3a ./src/systick/systick.o
 .ARM.attributes
                0x00000108       0x3a ./src/scheduler/scheduler.o
 .ARM.attributes
                0x00000142       0x3a ./src/circular_queue/circular_queue.o
 .ARM.attributes
                0x0000017c       0x3a ./src/app/led/led_app.o
 .ARM.attributes
                0x000001b6       0x3a ./src/app/key/key_app.o
 .ARM.attributes
                0x000001f0       0x3a ./src/app/debug_uart/debug_uart.o
 .ARM.attributes
                0x0000022a       0x3a ./src/app/adc/adc_app.o
 .ARM.attributes
                0x00000264       0x3a ./src/hal_entry.o
 .ARM.attributes
                0x0000029e       0x3a ./src/task.o
 .ARM.attributes
                0x000002d8       0x3a ./ra_gen/common_data.o
 .ARM.attributes
                0x00000312       0x3a ./ra_gen/hal_data.o
 .ARM.attributes
                0x0000034c       0x3a ./ra_gen/main.o
 .ARM.attributes
                0x00000386       0x3a ./ra_gen/pin_data.o
 .ARM.attributes
                0x000003c0       0x3a ./ra_gen/vector_data.o
 .ARM.attributes
                0x000003fa       0x3a ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .ARM.attributes
                0x00000434       0x3a ./ra/fsp/src/r_ioport/r_ioport.o
 .ARM.attributes
                0x0000046e       0x3a ./ra/fsp/src/r_gpt/r_gpt.o
 .ARM.attributes
                0x000004a8       0x3a ./ra/fsp/src/r_adc/r_adc.o
 .ARM.attributes
                0x000004e2       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .ARM.attributes
                0x0000051c       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .ARM.attributes
                0x00000556       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .ARM.attributes
                0x00000590       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .ARM.attributes
                0x000005ca       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .ARM.attributes
                0x00000604       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .ARM.attributes
                0x0000063e       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .ARM.attributes
                0x00000678       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .ARM.attributes
                0x000006b2       0x3a ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .ARM.attributes
                0x000006ec       0x3a ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .ARM.attributes
                0x00000726       0x3a ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
 .ARM.attributes
                0x00000760       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x00000782       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x000007a4       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x000007c6       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x000007e8       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x00000820       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000842       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
 .ARM.attributes
                0x0000087a       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-impure.o)
 .ARM.attributes
                0x000008b2       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
 .ARM.attributes
                0x000008ea       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
 .ARM.attributes
                0x00000922       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
 .ARM.attributes
                0x0000095a       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .ARM.attributes
                0x00000992       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
 .ARM.attributes
                0x000009ca       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
 .ARM.attributes
                0x00000a02       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
 .ARM.attributes
                0x00000a3a       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
 .ARM.attributes
                0x00000a72       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .ARM.attributes
                0x00000aaa       0x17 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strlen.o)
 .ARM.attributes
                0x00000ac1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
 .ARM.attributes
                0x00000af9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .ARM.attributes
                0x00000b31       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .ARM.attributes
                0x00000b69       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
 .ARM.attributes
                0x00000ba1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ctype_.o)
 .ARM.attributes
                0x00000bd9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
 .ARM.attributes
                0x00000c11       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
 .ARM.attributes
                0x00000c49       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
 .ARM.attributes
                0x00000c81       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 .ARM.attributes
                0x00000cb9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .ARM.attributes
                0x00000cf1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .ARM.attributes
                0x00000d29       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
 .ARM.attributes
                0x00000d61       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
 .ARM.attributes
                0x00000d99       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
 .ARM.attributes
                0x00000dd1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .ARM.attributes
                0x00000e09       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
 .ARM.attributes
                0x00000e41       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
 .ARM.attributes
                0x00000e79       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
 .ARM.attributes
                0x00000eb1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .ARM.attributes
                0x00000ee9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
 .ARM.attributes
                0x00000f21       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .ARM.attributes
                0x00000f59       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .ARM.attributes
                0x00000f91       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
 .ARM.attributes
                0x00000fc9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
 .ARM.attributes
                0x00001001       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
 .ARM.attributes
                0x00001039       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .ARM.attributes
                0x00001071       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .ARM.attributes
                0x000010a9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
 .ARM.attributes
                0x000010e1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .ARM.attributes
                0x00001119       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .ARM.attributes
                0x00001151       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
 .ARM.attributes
                0x00001189       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
 .ARM.attributes
                0x000011c1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
 .ARM.attributes
                0x000011f9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
 .ARM.attributes
                0x00001231       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .ARM.attributes
                0x00001253       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .ARM.attributes
                0x00001275       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .ARM.attributes
                0x00001297       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .ARM.attributes
                0x000012b9       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .ARM.attributes
                0x000012f1       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x00001329       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtend.o
 .ARM.attributes
                0x00001361       0x22 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard/crtn.o

.comment        0x00000000       0x49
 .comment       0x00000000       0x49 ./src/time/gpt0_timing.o
                                 0x4a (size before relaxing)
 .comment       0x00000049       0x4a ./src/time/gpt6_pwm.o
 .comment       0x00000049       0x4a ./src/systick/systick.o
 .comment       0x00000049       0x4a ./src/scheduler/scheduler.o
 .comment       0x00000049       0x4a ./src/circular_queue/circular_queue.o
 .comment       0x00000049       0x4a ./src/app/led/led_app.o
 .comment       0x00000049       0x4a ./src/app/key/key_app.o
 .comment       0x00000049       0x4a ./src/app/debug_uart/debug_uart.o
 .comment       0x00000049       0x4a ./src/app/adc/adc_app.o
 .comment       0x00000049       0x4a ./src/hal_entry.o
 .comment       0x00000049       0x4a ./src/task.o
 .comment       0x00000049       0x4a ./ra_gen/common_data.o
 .comment       0x00000049       0x4a ./ra_gen/hal_data.o
 .comment       0x00000049       0x4a ./ra_gen/main.o
 .comment       0x00000049       0x4a ./ra_gen/pin_data.o
 .comment       0x00000049       0x4a ./ra_gen/vector_data.o
 .comment       0x00000049       0x4a ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .comment       0x00000049       0x4a ./ra/fsp/src/r_ioport/r_ioport.o
 .comment       0x00000049       0x4a ./ra/fsp/src/r_gpt/r_gpt.o
 .comment       0x00000049       0x4a ./ra/fsp/src/r_adc/r_adc.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .comment       0x00000049       0x4a ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o

.debug_info     0x00000000    0x5d6b8
 .debug_info    0x00000000     0x44be ./src/time/gpt0_timing.o
 .debug_info    0x000044be     0x33da ./src/time/gpt6_pwm.o
 .debug_info    0x00007898      0x87e ./src/systick/systick.o
 .debug_info    0x00008116      0x21e ./src/scheduler/scheduler.o
 .debug_info    0x00008334      0x38c ./src/circular_queue/circular_queue.o
 .debug_info    0x000086c0     0x1298 ./src/app/led/led_app.o
 .debug_info    0x00009958      0x937 ./src/app/key/key_app.o
 .debug_info    0x0000a28f     0x36e9 ./src/app/debug_uart/debug_uart.o
 .debug_info    0x0000d978     0x39e6 ./src/app/adc/adc_app.o
 .debug_info    0x0001135e     0x1645 ./src/hal_entry.o
 .debug_info    0x000129a3     0x3994 ./src/task.o
 .debug_info    0x00016337     0x1244 ./ra_gen/common_data.o
 .debug_info    0x0001757b     0xb4c3 ./ra_gen/hal_data.o
 .debug_info    0x00022a3e       0xb8 ./ra_gen/main.o
 .debug_info    0x00022af6      0xa3b ./ra_gen/pin_data.o
 .debug_info    0x00023531      0x89e ./ra_gen/vector_data.o
 .debug_info    0x00023dcf     0xaaf4 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_info    0x0002e8c3     0x83d3 ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_info    0x00036c96     0xb5e4 ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_info    0x0004227a     0xb86b ./ra/fsp/src/r_adc/r_adc.o
 .debug_info    0x0004dae5     0x6258 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_info    0x00053d3d      0x955 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_info    0x00054692      0x1e9 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_info    0x0005487b     0x14c4 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_info    0x00055d3f       0xb1 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .debug_info    0x00055df0     0x223c ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_info    0x0005802c     0x408e ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_info    0x0005c0ba      0x1ec ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .debug_info    0x0005c2a6      0x149 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_info    0x0005c3ef      0x173 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .debug_info    0x0005c562     0x1156 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o

.debug_abbrev   0x00000000     0x482d
 .debug_abbrev  0x00000000      0x1ee ./src/time/gpt0_timing.o
 .debug_abbrev  0x000001ee      0x251 ./src/time/gpt6_pwm.o
 .debug_abbrev  0x0000043f      0x270 ./src/systick/systick.o
 .debug_abbrev  0x000006af      0x14e ./src/scheduler/scheduler.o
 .debug_abbrev  0x000007fd      0x17b ./src/circular_queue/circular_queue.o
 .debug_abbrev  0x00000978      0x149 ./src/app/led/led_app.o
 .debug_abbrev  0x00000ac1      0x146 ./src/app/key/key_app.o
 .debug_abbrev  0x00000c07      0x392 ./src/app/debug_uart/debug_uart.o
 .debug_abbrev  0x00000f99      0x29c ./src/app/adc/adc_app.o
 .debug_abbrev  0x00001235      0x2d8 ./src/hal_entry.o
 .debug_abbrev  0x0000150d      0x3d5 ./src/task.o
 .debug_abbrev  0x000018e2      0x12e ./ra_gen/common_data.o
 .debug_abbrev  0x00001a10      0x300 ./ra_gen/hal_data.o
 .debug_abbrev  0x00001d10       0x66 ./ra_gen/main.o
 .debug_abbrev  0x00001d76       0xf6 ./ra_gen/pin_data.o
 .debug_abbrev  0x00001e6c       0xa7 ./ra_gen/vector_data.o
 .debug_abbrev  0x00001f13      0x5bd ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_abbrev  0x000024d0      0x47a ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_abbrev  0x0000294a      0x652 ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_abbrev  0x00002f9c      0x5d0 ./ra/fsp/src/r_adc/r_adc.o
 .debug_abbrev  0x0000356c      0x3e8 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_abbrev  0x00003954      0x183 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_abbrev  0x00003ad7      0x11a ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_abbrev  0x00003bf1      0x254 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_abbrev  0x00003e45       0x5e ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .debug_abbrev  0x00003ea3      0x26c ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_abbrev  0x0000410f      0x277 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_abbrev  0x00004386       0x4d ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .debug_abbrev  0x000043d3       0xcc ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_abbrev  0x0000449f       0xe6 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .debug_abbrev  0x00004585      0x2a8 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o

.debug_aranges  0x00000000      0x748
 .debug_aranges
                0x00000000       0x28 ./src/time/gpt0_timing.o
 .debug_aranges
                0x00000028       0x28 ./src/time/gpt6_pwm.o
 .debug_aranges
                0x00000050       0x30 ./src/systick/systick.o
 .debug_aranges
                0x00000080       0x28 ./src/scheduler/scheduler.o
 .debug_aranges
                0x000000a8       0x50 ./src/circular_queue/circular_queue.o
 .debug_aranges
                0x000000f8       0x30 ./src/app/led/led_app.o
 .debug_aranges
                0x00000128       0x28 ./src/app/key/key_app.o
 .debug_aranges
                0x00000150       0x30 ./src/app/debug_uart/debug_uart.o
 .debug_aranges
                0x00000180       0x30 ./src/app/adc/adc_app.o
 .debug_aranges
                0x000001b0       0x28 ./src/hal_entry.o
 .debug_aranges
                0x000001d8       0x48 ./src/task.o
 .debug_aranges
                0x00000220       0x20 ./ra_gen/common_data.o
 .debug_aranges
                0x00000240       0x20 ./ra_gen/hal_data.o
 .debug_aranges
                0x00000260       0x20 ./ra_gen/main.o
 .debug_aranges
                0x00000280       0x18 ./ra_gen/pin_data.o
 .debug_aranges
                0x00000298       0x18 ./ra_gen/vector_data.o
 .debug_aranges
                0x000002b0       0x90 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_aranges
                0x00000340       0x90 ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_aranges
                0x000003d0       0xe8 ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_aranges
                0x000004b8       0xb8 ./ra/fsp/src/r_adc/r_adc.o
 .debug_aranges
                0x00000570       0x70 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_aranges
                0x000005e0       0x30 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_aranges
                0x00000610       0x28 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_aranges
                0x00000638       0x28 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_aranges
                0x00000660       0x18 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .debug_aranges
                0x00000678       0x20 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_aranges
                0x00000698       0x28 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_aranges
                0x000006c0       0x18 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .debug_aranges
                0x000006d8       0x20 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_aranges
                0x000006f8       0x28 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .debug_aranges
                0x00000720       0x28 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o

.debug_ranges   0x00000000     0x13c8
 .debug_ranges  0x00000000       0x18 ./src/time/gpt0_timing.o
 .debug_ranges  0x00000018       0x18 ./src/time/gpt6_pwm.o
 .debug_ranges  0x00000030       0x58 ./src/systick/systick.o
 .debug_ranges  0x00000088       0x30 ./src/scheduler/scheduler.o
 .debug_ranges  0x000000b8       0x70 ./src/circular_queue/circular_queue.o
 .debug_ranges  0x00000128       0x20 ./src/app/led/led_app.o
 .debug_ranges  0x00000148       0x18 ./src/app/key/key_app.o
 .debug_ranges  0x00000160       0x20 ./src/app/debug_uart/debug_uart.o
 .debug_ranges  0x00000180       0x20 ./src/app/adc/adc_app.o
 .debug_ranges  0x000001a0       0x48 ./src/hal_entry.o
 .debug_ranges  0x000001e8       0x78 ./src/task.o
 .debug_ranges  0x00000260       0x10 ./ra_gen/common_data.o
 .debug_ranges  0x00000270       0x10 ./ra_gen/hal_data.o
 .debug_ranges  0x00000280       0x10 ./ra_gen/main.o
 .debug_ranges  0x00000290      0x540 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_ranges  0x000007d0      0x110 ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_ranges  0x000008e0      0x408 ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_ranges  0x00000ce8      0x3b8 ./ra/fsp/src/r_adc/r_adc.o
 .debug_ranges  0x000010a0      0x158 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_ranges  0x000011f8       0x20 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_ranges  0x00001218       0x48 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_ranges  0x00001260       0x58 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_ranges  0x000012b8       0x70 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_ranges  0x00001328       0x48 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_ranges  0x00001370       0x10 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_ranges  0x00001380       0x18 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .debug_ranges  0x00001398       0x30 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o

.debug_line     0x00000000     0xa1fc
 .debug_line    0x00000000      0x2e6 ./src/time/gpt0_timing.o
 .debug_line    0x000002e6      0x31d ./src/time/gpt6_pwm.o
 .debug_line    0x00000603      0x458 ./src/systick/systick.o
 .debug_line    0x00000a5b      0x36f ./src/scheduler/scheduler.o
 .debug_line    0x00000dca      0x36a ./src/circular_queue/circular_queue.o
 .debug_line    0x00001134      0x277 ./src/app/led/led_app.o
 .debug_line    0x000013ab      0x261 ./src/app/key/key_app.o
 .debug_line    0x0000160c      0x4e4 ./src/app/debug_uart/debug_uart.o
 .debug_line    0x00001af0      0x3ab ./src/app/adc/adc_app.o
 .debug_line    0x00001e9b      0x48b ./src/hal_entry.o
 .debug_line    0x00002326      0x6b9 ./src/task.o
 .debug_line    0x000029df      0x269 ./ra_gen/common_data.o
 .debug_line    0x00002c48      0x346 ./ra_gen/hal_data.o
 .debug_line    0x00002f8e       0x5e ./ra_gen/main.o
 .debug_line    0x00002fec      0x1db ./ra_gen/pin_data.o
 .debug_line    0x000031c7       0xf1 ./ra_gen/vector_data.o
 .debug_line    0x000032b8     0x16fa ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_line    0x000049b2      0xb18 ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_line    0x000054ca     0x15ec ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_line    0x00006ab6     0x13e5 ./ra/fsp/src/r_adc/r_adc.o
 .debug_line    0x00007e9b      0x9f3 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_line    0x0000888e      0x25f ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_line    0x00008aed      0x27d ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_line    0x00008d6a      0x2d8 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_line    0x00009042      0x141 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
 .debug_line    0x00009183      0x3db ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_line    0x0000955e      0x383 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_line    0x000098e1       0xf7 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
 .debug_line    0x000099d8      0x182 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_line    0x00009b5a      0x1c5 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .debug_line    0x00009d1f      0x4dd ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o

.debug_str      0x00000000    0x11611
 .debug_str     0x00000000     0x3052 ./src/time/gpt0_timing.o
                               0x3132 (size before relaxing)
 .debug_str     0x00003052      0x1df ./src/time/gpt6_pwm.o
                               0x1a1d (size before relaxing)
 .debug_str     0x00003231      0x2ec ./src/systick/systick.o
                                0x5b2 (size before relaxing)
 .debug_str     0x0000351d       0xa8 ./src/scheduler/scheduler.o
                                0x2ab (size before relaxing)
 .debug_str     0x000035c5       0xcd ./src/circular_queue/circular_queue.o
                                0x2bd (size before relaxing)
 .debug_str     0x00003692     0x1c48 ./src/app/led/led_app.o
                               0x3341 (size before relaxing)
 .debug_str     0x000052da       0x53 ./src/app/key/key_app.o
                               0x17dd (size before relaxing)
 .debug_str     0x0000532d      0xefc ./src/app/debug_uart/debug_uart.o
                               0x4199 (size before relaxing)
 .debug_str     0x00006229     0x1c51 ./src/app/adc/adc_app.o
                               0x3983 (size before relaxing)
 .debug_str     0x00007e7a      0x183 ./src/hal_entry.o
                               0x34ff (size before relaxing)
 .debug_str     0x00007ffd       0xf9 ./src/task.o
                               0x4261 (size before relaxing)
 .debug_str     0x000080f6       0x39 ./ra_gen/common_data.o
                               0x332f (size before relaxing)
 .debug_str     0x0000812f     0x3b69 ./ra_gen/hal_data.o
                               0xaeb6 (size before relaxing)
 .debug_str     0x0000bc98       0x16 ./ra_gen/main.o
                                0x1d7 (size before relaxing)
 .debug_str     0x0000bcae      0x660 ./ra_gen/pin_data.o
                               0x1d90 (size before relaxing)
 .debug_str     0x0000c30e       0x68 ./ra_gen/vector_data.o
                               0x1d92 (size before relaxing)
 .debug_str     0x0000c376     0x23a5 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
                               0x6d64 (size before relaxing)
 .debug_str     0x0000e71b      0x77b ./ra/fsp/src/r_ioport/r_ioport.o
                               0x5c7b (size before relaxing)
 .debug_str     0x0000ee96      0xaf7 ./ra/fsp/src/r_gpt/r_gpt.o
                               0x6ef0 (size before relaxing)
 .debug_str     0x0000f98d      0x560 ./ra/fsp/src/r_adc/r_adc.o
                               0x87c5 (size before relaxing)
 .debug_str     0x0000feed      0x7da ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
                               0x277e (size before relaxing)
 .debug_str     0x000106c7       0xfd ./ra/fsp/src/bsp/mcu/all/bsp_common.o
                               0x1c24 (size before relaxing)
 .debug_str     0x000107c4       0xab ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
                                0x300 (size before relaxing)
 .debug_str     0x0001086f      0x1e4 ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
                               0x218b (size before relaxing)
 .debug_str     0x00010a53       0x23 ./ra/fsp/src/bsp/mcu/all/bsp_io.o
                                0x200 (size before relaxing)
 .debug_str     0x00010a76      0x495 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
                               0x27b5 (size before relaxing)
 .debug_str     0x00010f0b       0x88 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
                               0x1b63 (size before relaxing)
 .debug_str     0x00010f93      0x197 ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o
                                0x35c (size before relaxing)
 .debug_str     0x0001112a       0x96 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
                                0x261 (size before relaxing)
 .debug_str     0x000111c0       0x89 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
                                0x251 (size before relaxing)
 .debug_str     0x00011249      0x3c8 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
                                0x959 (size before relaxing)

.debug_frame    0x00000000     0x2370
 .debug_frame   0x00000000       0x3c ./src/time/gpt0_timing.o
 .debug_frame   0x0000003c       0x50 ./src/time/gpt6_pwm.o
 .debug_frame   0x0000008c       0x54 ./src/systick/systick.o
 .debug_frame   0x000000e0       0x4c ./src/scheduler/scheduler.o
 .debug_frame   0x0000012c       0xb4 ./src/circular_queue/circular_queue.o
 .debug_frame   0x000001e0       0x40 ./src/app/led/led_app.o
 .debug_frame   0x00000220       0x50 ./src/app/key/key_app.o
 .debug_frame   0x00000270       0x70 ./src/app/debug_uart/debug_uart.o
 .debug_frame   0x000002e0       0x58 ./src/app/adc/adc_app.o
 .debug_frame   0x00000338       0x38 ./src/hal_entry.o
 .debug_frame   0x00000370       0xf8 ./src/task.o
 .debug_frame   0x00000468       0x20 ./ra_gen/common_data.o
 .debug_frame   0x00000488       0x20 ./ra_gen/hal_data.o
 .debug_frame   0x000004a8       0x28 ./ra_gen/main.o
 .debug_frame   0x000004d0      0x1b8 ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_frame   0x00000688      0x184 ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_frame   0x0000080c      0x29c ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_frame   0x00000aa8      0x1dc ./ra/fsp/src/r_adc/r_adc.o
 .debug_frame   0x00000c84      0x104 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_frame   0x00000d88       0x40 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_frame   0x00000dc8       0x44 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_frame   0x00000e0c       0x3c ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_frame   0x00000e48       0x30 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_frame   0x00000e78       0x48 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_frame   0x00000ec0       0x28 ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_frame   0x00000ee8       0x38 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o
 .debug_frame   0x00000f20       0x44 ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
 .debug_frame   0x00000f64       0xac d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x00001010       0x50 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x00001060       0xc4 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x00001124       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00001150       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_udivmoddi4.o)
 .debug_frame   0x00001188       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-errno.o)
 .debug_frame   0x000011a8       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memcpy-stub.o)
 .debug_frame   0x000011d0       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memset.o)
 .debug_frame   0x000011f0       0xa0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_float.o)
 .debug_frame   0x00001290       0x60 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfprintf_i.o)
 .debug_frame   0x000012f0       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_float.o)
 .debug_frame   0x00001330       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sf_nan.o)
 .debug_frame   0x00001350       0x6c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sprintf.o)
 .debug_frame   0x000013bc       0x78 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sscanf.o)
 .debug_frame   0x00001434       0x88 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-stdio.o)
 .debug_frame   0x000014bc       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strncmp.o)
 .debug_frame   0x000014e4      0x12c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtod.o)
 .debug_frame   0x00001610       0x64 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtol.o)
 .debug_frame   0x00001674       0x4c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-vsnprintf.o)
 .debug_frame   0x000016c0       0x88 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-dtoa.o)
 .debug_frame   0x00001748       0x84 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-gethex.o)
 .debug_frame   0x000017cc       0x74 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-gdtoa-hexnan.o)
 .debug_frame   0x00001840       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lnumeric.o)
 .debug_frame   0x00001860       0x48 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-locale.o)
 .debug_frame   0x000018a8       0x40 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-localeconv.o)
 .debug_frame   0x000018e8       0x30 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-malloc.o)
 .debug_frame   0x00001918       0x48 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mbtowc_r.o)
 .debug_frame   0x00001960       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memchr-stub.o)
 .debug_frame   0x00001988      0x25c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mprec.o)
 .debug_frame   0x00001be4       0x30 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-callocr.o)
 .debug_frame   0x00001c14       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-freer.o)
 .debug_frame   0x00001c4c       0x4c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-mallocr.o)
 .debug_frame   0x00001c98       0x90 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfprintf.o)
 .debug_frame   0x00001d28       0x74 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-svfscanf.o)
 .debug_frame   0x00001d9c       0x64 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-vfscanf_i.o)
 .debug_frame   0x00001e00       0x5c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-reent.o)
 .debug_frame   0x00001e5c       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-s_nan.o)
 .debug_frame   0x00001e7c       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sbrkr.o)
 .debug_frame   0x00001ea8       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-sccl.o)
 .debug_frame   0x00001ed4       0x64 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-strtoul.o)
 .debug_frame   0x00001f38       0x5c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-ungetc.o)
 .debug_frame   0x00001f94       0x3c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-wctomb_r.o)
 .debug_frame   0x00001fd0      0x14c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-findfp.o)
 .debug_frame   0x0000211c       0xb0 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-lock.o)
 .debug_frame   0x000021cc       0x28 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-memmove.o)
 .debug_frame   0x000021f4       0x30 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-mlock.o)
 .debug_frame   0x00002224       0x3c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-reallocr.o)
 .debug_frame   0x00002260       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/../../../../arm-none-eabi/lib/thumb/v8-m.main+fp/hard\libg_nano.a(lib_a-nano-msizer.o)
 .debug_frame   0x00002280       0x20 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_unorddf2.o)
 .debug_frame   0x000022a0       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixdfsi.o)
 .debug_frame   0x000022c4       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x000022e8       0x24 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_arm_truncdfsf2.o)
 .debug_frame   0x0000230c       0x38 d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixdfdi.o)
 .debug_frame   0x00002344       0x2c d:/ryh/updedate_app/e2studio/10 2021.10/bin/../lib/gcc/arm-none-eabi/10.3.1/thumb/v8-m.main+fp/hard\libgcc.a(_fixunsdfdi.o)

.debug_loc      0x00000000     0x5c69
 .debug_loc     0x00000000       0xdf ./src/time/gpt6_pwm.o
 .debug_loc     0x000000df      0x10a ./src/systick/systick.o
 .debug_loc     0x000001e9       0x45 ./src/scheduler/scheduler.o
 .debug_loc     0x0000022e      0x2e9 ./src/circular_queue/circular_queue.o
 .debug_loc     0x00000517       0x56 ./src/app/key/key_app.o
 .debug_loc     0x0000056d       0xe7 ./src/app/debug_uart/debug_uart.o
 .debug_loc     0x00000654       0x2f ./src/app/adc/adc_app.o
 .debug_loc     0x00000683       0x88 ./src/hal_entry.o
 .debug_loc     0x0000070b      0x173 ./src/task.o
 .debug_loc     0x0000087e     0x152c ./ra/fsp/src/r_sci_uart/r_sci_uart.o
 .debug_loc     0x00001daa      0xba1 ./ra/fsp/src/r_ioport/r_ioport.o
 .debug_loc     0x0000294b     0x171c ./ra/fsp/src/r_gpt/r_gpt.o
 .debug_loc     0x00004067     0x1245 ./ra/fsp/src/r_adc/r_adc.o
 .debug_loc     0x000052ac      0x373 ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o
 .debug_loc     0x0000561f       0x22 ./ra/fsp/src/bsp/mcu/all/bsp_common.o
 .debug_loc     0x00005641      0x178 ./ra/fsp/src/bsp/mcu/all/bsp_delay.o
 .debug_loc     0x000057b9       0xbf ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o
 .debug_loc     0x00005878      0x105 ./ra/fsp/src/bsp/mcu/all/bsp_irq.o
 .debug_loc     0x0000597d      0x105 ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o
 .debug_loc     0x00005a82      0x11a ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o
 .debug_loc     0x00005b9c       0xcd ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o
