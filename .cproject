<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638" moduleId="org.eclipse.cdt.core.settings" name="Debug">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.debug" cleanCommand="${cross_rm} -rf" description="" id="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638" name="Debug" parent="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug">
					<folderInfo id="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638." name="/" resourcePath="">
						<toolChain id="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.debug.1924942760" name="GCC ARM Embedded" superClass="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.debug">
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.503562495" name="Create flash image" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.411250190" name="Create extended listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.204639021" name="Print size" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand.2007977706" name="Echo tool command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.1081458414" name="Optimization Level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level" value="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.more" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.341678123" name="Message length (-fmessage-length=0)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.270802332" name="'char' is signed (-fsigned-char)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.1052137" name="Function sections (-ffunction-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.1985465167" name="Data sections (-fdata-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.1364678099" name="Debug level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.463609468" name="Debug format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.905954329" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name" value="GNU Tools for ARM Embedded Processors" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.403892356" name="Architecture" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.architecture" value="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.arm" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.180256340" name="Arm family (-mcpu)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.mcpu.cortex-m33" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.771279512" name="Instruction set" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.thumb" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.1334347170" name="Prefix" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.1817077299" name="C compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.c" value="gcc" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.2064818969" name="C++ compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp" value="g++" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing.856340693" name="Disable optimizations based on the type of expressions (-fno-strict-aliasing)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.877164391" name="Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar" value="ar" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.975915878" name="Hex/Bin converter" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy" value="objcopy" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.672585711" name="Listing generator" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump" value="objdump" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.1156149253" name="Size command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.size" value="size" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.1219001709" name="Build command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.make" value="make" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.490828754" name="Remove command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm" value="rm" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused.1935704883" name="Warn on various unused elements (-Wunused)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized.672157018" name="Warn on uninitialized variables (-Wuninitialised)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn.1227070330" name="Enable all common warnings (-Wall)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn.493961856" name="Enable extra warnings (-Wextra)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration.1301024279" name="Warn on undeclared global function (-Wmissing-declaration)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion.1824733278" name="Warn on implicit conversions (-Wconversion)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith.1477034784" name="Warn if pointer arithmetic (-Wpointer-arith)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow.789537503" name="Warn if shadowed variable (-Wshadow)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop.740520659" name="Warn if suspicious logical ops (-Wlogical-op)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn.810323533" name="Warn if struct is returned (-Wagreggate-return)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal.70459574" name="Warn if floats are compared as equal (-Wfloat-equal)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.518653212" name="FPU Type" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.fpv5spd16" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.903361780" name="Float ABI" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.hard" valueType="enumerated"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_ELF" id="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform.522170063" isAbstract="false" osList="all" superClass="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform"/>
							<builder buildPath="${workspace_loc:/Ability_Assessment_Project}/Debug" id="com.renesas.cdt.managedbuild.gnuarm.builder.338578612" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU Make 构建器" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.renesas.cdt.managedbuild.gnuarm.builder"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.1778365264" name="GNU Arm Cross Assembler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor.1080936104" name="Use preprocessor" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.defs.1311935918" name="Defined symbols (-D)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.defs" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_RENESAS_RA_"/>
									<listOptionValue builtIn="false" value="_RA_CORE=CM33"/>
									<listOptionValue builtIn="false" value="_RA_ORDINAL=1"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.include.paths.1611351191" name="Include paths (-I)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;.&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/api}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/instances}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/arm/CMSIS_6/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_gen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg/bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg}&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input.1255729902" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} -c ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -x c ${INPUTS}" id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.566306915" name="GNU Arm Cross C Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.std.70064271" name="Language standard" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.std" useByScannerDiscovery="true" value="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.std.c99" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.defs.795599071" name="Defined symbols (-D)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_RENESAS_RA_"/>
									<listOptionValue builtIn="false" value="_RA_CORE=CM33"/>
									<listOptionValue builtIn="false" value="_RA_ORDINAL=1"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.include.paths.2005762570" name="Include paths (-I)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;.&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/api}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/instances}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/arm/CMSIS_6/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_gen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg/bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg}&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.840314022" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} -c ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -x c++ ${INPUTS}" id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.335636039" name="GNU Arm Cross C++ Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.std.944859737" name="Language standard" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.std" useByScannerDiscovery="true" value="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.std.cpp11" valueType="enumerated"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -Wl,--start-group ${INPUTS} -Wl,--end-group" id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.359864756" name="GNU Arm Cross C Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections.1136566709" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usenewlibnano.302984453" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usenewlibnano" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.scriptfile.1324946196" name="Script files (-T)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;fsp.ld&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.paths.1270393665" name="Library search path (-L)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.paths" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/script&quot;"/>
								</option>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.useprintffloat.861589512" name="Use float with nano printf (-u _printf_float)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.useprintffloat" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usescanffloat.2014044248" name="Use float with nano scanf (-u _scanf_float)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usescanffloat" value="true" valueType="boolean"/>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input.29323132" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
									<additionalInput kind="additionaldependency" paths="$(LINKER_SCRIPT)"/>
								</inputType>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -Wl,--start-group ${INPUTS} -Wl,--end-group" id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.35783365" name="GNU Arm Cross C++ Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections.6767682" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.usenewlibnano.435936747" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.usenewlibnano" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.scriptfile.743843286" name="Script files (-T)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;fsp.ld&quot;"/>
								</option>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.1836062336" name="GNU Arm Cross Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.1100718897" name="GNU Arm Cross Create Flash Image" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createflash.choice.77624389" name="Output file format (-O)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createflash.choice" value="ilg.gnuarmeclipse.managedbuild.cross.option.createflash.choice.srec" valueType="enumerated"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.2129662810" name="GNU Arm Cross Create Listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source.232442118" name="Display source (--source|-S)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders.1568101996" name="Display all headers (--all-headers|-x)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle.762292503" name="Demangle names (--demangle|-C)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers.603070029" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide.1042017476" name="Wide lines (--wide|-w)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.328219820" name="GNU Arm Cross Print Size" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format.1706421940" name="Size format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format"/>
							</tool>
						</toolChain>
					</folderInfo>
					<folderInfo id="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638./ra/arm" name="arm" resourcePath="ra/arm">
						<toolChain id="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.debug.1305658057" name="GCC ARM Embedded" superClass="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.debug" unusedChildren="">
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.503562495.691338331" name="Create flash image" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.503562495"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.411250190.2027013784" name="Create extended listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.411250190"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.204639021.75754300" name="Print size" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.204639021"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand.2007977706.784343356" name="Echo tool command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand.2007977706"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.1081458414.1030563734" name="Optimization Level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.1081458414"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.341678123.1801019203" name="Message length (-fmessage-length=0)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.341678123"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.270802332.616613368" name="'char' is signed (-fsigned-char)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.270802332"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.1052137.1901005944" name="Function sections (-ffunction-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.1052137"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.1985465167.1078326788" name="Data sections (-fdata-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.1985465167"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.1364678099.485587389" name="Debug level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.1364678099"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.463609468.63960174" name="Debug format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.463609468"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.905954329.2058387305" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.905954329"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.403892356.397282974" name="Architecture" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.403892356"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.180256340.1821734182" name="Arm family (-mcpu)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.180256340"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.771279512.545177554" name="Instruction set" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.771279512"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.1334347170.1798526171" name="Prefix" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.1334347170"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.1817077299.1757194047" name="C compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.1817077299"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.2064818969.1672141452" name="C++ compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.2064818969"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing.856340693.980837300" name="Disable optimizations based on the type of expressions (-fno-strict-aliasing)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing.856340693"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.877164391.2071993902" name="Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.877164391"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.975915878.1943580352" name="Hex/Bin converter" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.975915878"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.672585711.508600869" name="Listing generator" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.672585711"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.1156149253.1370554759" name="Size command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.1156149253"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.1219001709.713798442" name="Build command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.1219001709"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.490828754.1752609618" name="Remove command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.490828754"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused.1935704883.790463738" name="Warn on various unused elements (-Wunused)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused.1935704883"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized.672157018.1294721961" name="Warn on uninitialized variables (-Wuninitialised)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized.672157018"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn.1227070330.1278665941" name="Enable all common warnings (-Wall)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn.1227070330"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn.493961856.353279685" name="Enable extra warnings (-Wextra)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn.493961856"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration.1301024279.1175159964" name="Warn on undeclared global function (-Wmissing-declaration)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration.1301024279"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion.1824733278.1705739515" name="Warn on implicit conversions (-Wconversion)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion.1824733278"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith.1477034784.870136372" name="Warn if pointer arithmetic (-Wpointer-arith)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith.1477034784"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow.789537503.246696697" name="Warn if shadowed variable (-Wshadow)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow.789537503"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop.740520659.1918929523" name="Warn if suspicious logical ops (-Wlogical-op)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop.740520659"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn.810323533.257346570" name="Warn if struct is returned (-Wagreggate-return)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn.810323533"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal.70459574.709047590" name="Warn if floats are compared as equal (-Wfloat-equal)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal.70459574"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.518653212.57187990" name="FPU Type" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.518653212"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.903361780.1473115091" name="Float ABI" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.903361780"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_ELF" id="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform" isAbstract="false" osList="all" superClass="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.2105186131" name="GNU Arm Cross Assembler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.1778365264">
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input.1543801590" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.655146305" name="GNU Arm Cross C Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.566306915">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.other.1251858016" name="Other compiler flags" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.other" value="-w" valueType="string"/>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.299316914" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.588983081" name="GNU Arm Cross C++ Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.335636039">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.other.687876544" name="Other compiler flags" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.other" value="-w" valueType="string"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.1590727222" name="GNU Arm Cross C Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.359864756"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.369238587" name="GNU Arm Cross C++ Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.35783365"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.694273614" name="GNU Arm Cross Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.1836062336"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.708271188" name="GNU Arm Cross Create Flash Image" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.1100718897"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.1121753160" name="GNU Arm Cross Create Listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.2129662810"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.1043962054" name="GNU Arm Cross Print Size" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.328219820"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="ra"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="ra_gen"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="src"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
			<storageModule moduleId="com.renesas.cdt.managedbuild.core.toolchainInfo"/>
		</cconfiguration>
		<cconfiguration id="com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********" moduleId="org.eclipse.cdt.core.settings" name="Release">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GNU_ELF" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactName="${ProjName}" buildArtefactType="org.eclipse.cdt.build.core.buildArtefactType.exe" buildProperties="org.eclipse.cdt.build.core.buildArtefactType=org.eclipse.cdt.build.core.buildArtefactType.exe,org.eclipse.cdt.build.core.buildType=org.eclipse.cdt.build.core.buildType.release" cleanCommand="${cross_rm} -rf" description="" id="com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********" name="Release" parent="com.renesas.cdt.managedbuild.gnuarm.config.elf.release">
					<folderInfo id="com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********." name="/" resourcePath="">
						<toolChain id="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.release.824104455" name="GCC ARM Embedded" superClass="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.release">
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.1420089066" name="Create flash image" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.1755553576" name="Create extended listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.1610803407" name="Print size" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand.1612529740" name="Echo tool command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.2066689544" name="Optimization Level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level" value="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.more" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.377564605" name="Message length (-fmessage-length=0)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.995933050" name="'char' is signed (-fsigned-char)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.1243249097" name="Function sections (-ffunction-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.270051534" name="Data sections (-fdata-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.949594664" name="Debug level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.1478825190" name="Debug format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.1341778987" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name" value="GNU Tools for ARM Embedded Processors" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.1617963998" name="Architecture" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.architecture" value="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.arm" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.2108333837" name="Arm family (-mcpu)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.mcpu.cortex-m33" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.1027135487" name="Instruction set" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.thumb" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.629774602" name="Prefix" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix" value="arm-none-eabi-" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.819121740" name="C compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.c" value="gcc" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.446323218" name="C++ compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp" value="g++" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing.1991859627" name="Disable optimizations based on the type of expressions (-fno-strict-aliasing)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.264370117" name="Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar" value="ar" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.1935503225" name="Hex/Bin converter" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy" value="objcopy" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.1735060320" name="Listing generator" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump" value="objdump" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.885856000" name="Size command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.size" value="size" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.510921593" name="Build command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.make" value="make" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.1863618178" name="Remove command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm" value="rm" valueType="string"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused.1252714846" name="Warn on various unused elements (-Wunused)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized.1910005821" name="Warn on uninitialized variables (-Wuninitialised)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn.664593249" name="Enable all common warnings (-Wall)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn.1314098303" name="Enable extra warnings (-Wextra)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration.910670762" name="Warn on undeclared global function (-Wmissing-declaration)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion.724699635" name="Warn on implicit conversions (-Wconversion)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith.590031149" name="Warn if pointer arithmetic (-Wpointer-arith)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow.1106025328" name="Warn if shadowed variable (-Wshadow)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop.1096858334" name="Warn if suspicious logical ops (-Wlogical-op)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn.170139181" name="Warn if struct is returned (-Wagreggate-return)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal.1919286054" name="Warn if floats are compared as equal (-Wfloat-equal)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal" value="true" valueType="boolean"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.1988746817" name="FPU Type" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.fpv5spd16" valueType="enumerated"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.1425181788" name="Float ABI" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi" value="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.hard" valueType="enumerated"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_ELF" id="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform.1834104844" isAbstract="false" osList="all" superClass="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform"/>
							<builder buildPath="${workspace_loc:/Ability_Assessment_Project}/Release" id="com.renesas.cdt.managedbuild.gnuarm.builder.446937189" keepEnvironmentInBuildfile="false" managedBuildOn="true" name="GNU Make 构建器" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.renesas.cdt.managedbuild.gnuarm.builder"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.531859422" name="GNU Arm Cross Assembler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor.553666441" name="Use preprocessor" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.usepreprocessor" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.defs.1604449455" name="Defined symbols (-D)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.defs" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_RENESAS_RA_"/>
									<listOptionValue builtIn="false" value="_RA_CORE=CM33"/>
									<listOptionValue builtIn="false" value="_RA_ORDINAL=1"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.include.paths.151877540" name="Include paths (-I)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.assembler.include.paths" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;.&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/api}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/instances}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/arm/CMSIS_6/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_gen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg/bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg}&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input.64576573" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} -c ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -x c ${INPUTS}" id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.521544308" name="GNU Arm Cross C Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.std.169271219" name="Language standard" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.std" useByScannerDiscovery="true" value="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.std.c99" valueType="enumerated"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.defs.847088715" name="Defined symbols (-D)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.defs" useByScannerDiscovery="true" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="_RENESAS_RA_"/>
									<listOptionValue builtIn="false" value="_RA_CORE=CM33"/>
									<listOptionValue builtIn="false" value="_RA_ORDINAL=1"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.include.paths.924873953" name="Include paths (-I)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.include.paths" useByScannerDiscovery="true" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/src}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;.&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/api}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/fsp/inc/instances}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra/arm/CMSIS_6/CMSIS/Core/Include}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_gen}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg/bsp}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}/ra_cfg/fsp_cfg}&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.588509767" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} -c ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -x c++ ${INPUTS}" id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.898563844" name="GNU Arm Cross C++ Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.std.1602332206" name="Language standard" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.std" useByScannerDiscovery="true" value="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.std.cpp11" valueType="enumerated"/>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -Wl,--start-group ${INPUTS} -Wl,--end-group" id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.1346517925" name="GNU Arm Cross C Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections.1900542101" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.gcsections" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usenewlibnano.85726554" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.usenewlibnano" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.scriptfile.644413294" name="Script files (-T)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;fsp.ld&quot;"/>
								</option>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.paths.1172453168" name="Library search path (-L)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.linker.paths" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${workspace_loc:/${ProjName}}/script&quot;"/>
								</option>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input.1247747012" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.input">
									<additionalInput kind="additionalinputdependency" paths="$(USER_OBJS)"/>
									<additionalInput kind="additionalinput" paths="$(LIBS)"/>
									<additionalInput kind="additionaldependency" paths="$(LINKER_SCRIPT)"/>
								</inputType>
							</tool>
							<tool commandLinePattern="${COMMAND} ${cross_toolchain_flags} ${FLAGS} ${OUTPUT_FLAG} ${OUTPUT_PREFIX}${OUTPUT} -Wl,--start-group ${INPUTS} -Wl,--end-group" id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.1364339306" name="GNU Arm Cross C++ Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections.510656901" name="Remove unused sections (-Xlinker --gc-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.gcsections" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.usenewlibnano.780446288" name="Use newlib-nano (--specs=nano.specs)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.usenewlibnano" value="true" valueType="boolean"/>
								<option IS_BUILTIN_EMPTY="false" IS_VALUE_EMPTY="false" id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.scriptfile.2074497060" name="Script files (-T)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.linker.scriptfile" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;fsp.ld&quot;"/>
								</option>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.156338365" name="GNU Arm Cross Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.34423287" name="GNU Arm Cross Create Flash Image" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createflash.choice.884553592" name="Output file format (-O)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createflash.choice" value="ilg.gnuarmeclipse.managedbuild.cross.option.createflash.choice.srec" valueType="enumerated"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.1711085275" name="GNU Arm Cross Create Listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source.224434439" name="Display source (--source|-S)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.source" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders.253953097" name="Display all headers (--all-headers|-x)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.allheaders" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle.1656665007" name="Demangle names (--demangle|-C)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.demangle" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers.374535976" name="Display line numbers (--line-numbers|-l)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.linenumbers" value="true" valueType="boolean"/>
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide.1227390253" name="Wide lines (--wide|-w)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.createlisting.wide" value="true" valueType="boolean"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.1347463793" name="GNU Arm Cross Print Size" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format.1986647686" name="Size format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.printsize.format"/>
							</tool>
						</toolChain>
					</folderInfo>
					<folderInfo id="com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********./ra/arm" name="arm" resourcePath="ra/arm">
						<toolChain id="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.release.1982421287" name="GCC ARM Embedded" superClass="com.renesas.cdt.managedbuild.gnuarm.toolchain.elf.release" unusedChildren="">
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.1420089066.1833915600" name="Create flash image" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createflash.1420089066"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.1755553576.1897481618" name="Create extended listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.createlisting.1755553576"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.1610803407.2089042214" name="Print size" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.printsize.1610803407"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand.1612529740.1555803003" name="Echo tool command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.addtools.showCommand.1612529740"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.2066689544.342106368" name="Optimization Level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.level.2066689544"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.377564605.2145806894" name="Message length (-fmessage-length=0)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.messagelength.377564605"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.995933050.1298234171" name="'char' is signed (-fsigned-char)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.signedchar.995933050"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.1243249097.1175498142" name="Function sections (-ffunction-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.functionsections.1243249097"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.270051534.744186532" name="Data sections (-fdata-sections)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.datasections.270051534"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.949594664.865808893" name="Debug level" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.level.949594664"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.1478825190.355633375" name="Debug format" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.debugging.format.1478825190"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.1341778987.241658948" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.toolchain.name.1341778987"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.1617963998.791396068" name="Architecture" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.architecture.1617963998"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.2108333837.1322588149" name="Arm family (-mcpu)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.family.2108333837"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.1027135487.948823191" name="Instruction set" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.instructionset.1027135487"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.629774602.1946214657" name="Prefix" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.prefix.629774602"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.819121740.821707676" name="C compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.c.819121740"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.446323218.929066440" name="C++ compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.cpp.446323218"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing.1991859627.636124869" name="Disable optimizations based on the type of expressions (-fno-strict-aliasing)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.optimization.nostrictaliasing.1991859627"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.264370117.231961696" name="Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.ar.264370117"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.1935503225.212295535" name="Hex/Bin converter" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objcopy.1935503225"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.1735060320.360843166" name="Listing generator" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.objdump.1735060320"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.885856000.622900695" name="Size command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.size.885856000"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.510921593.157971677" name="Build command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.make.510921593"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.1863618178.274784226" name="Remove command" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.command.rm.1863618178"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused.1252714846.769683601" name="Warn on various unused elements (-Wunused)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.unused.1252714846"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized.1910005821.1261190648" name="Warn on uninitialized variables (-Wuninitialised)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.uninitialized.1910005821"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn.664593249.1287925220" name="Enable all common warnings (-Wall)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.allwarn.664593249"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn.1314098303.1439333781" name="Enable extra warnings (-Wextra)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.extrawarn.1314098303"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration.910670762.771000601" name="Warn on undeclared global function (-Wmissing-declaration)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.missingdeclaration.910670762"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion.724699635.801581424" name="Warn on implicit conversions (-Wconversion)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.conversion.724699635"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith.590031149.1355253025" name="Warn if pointer arithmetic (-Wpointer-arith)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.pointerarith.590031149"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow.1106025328.305290698" name="Warn if shadowed variable (-Wshadow)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.shadow.1106025328"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop.1096858334.623668100" name="Warn if suspicious logical ops (-Wlogical-op)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.logicalop.1096858334"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn.170139181.931539264" name="Warn if struct is returned (-Wagreggate-return)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.agreggatereturn.170139181"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal.1919286054.1781852978" name="Warn if floats are compared as equal (-Wfloat-equal)" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.warnings.floatequal.1919286054"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.1988746817.350412331" name="FPU Type" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.unit.1988746817"/>
							<option id="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.1425181788.1731617951" name="Float ABI" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.arm.target.fpu.abi.1425181788"/>
							<targetPlatform archList="all" binaryParser="org.eclipse.cdt.core.GNU_ELF" id="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform" isAbstract="false" osList="all" superClass="ilg.gnuarmeclipse.managedbuild.cross.targetPlatform"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.660329593" name="GNU Arm Cross Assembler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.531859422">
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input.1641599304" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.assembler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.724261171" name="GNU Arm Cross C Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.521544308">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.other.440536268" name="Other compiler flags" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.c.compiler.other" value="-w" valueType="string"/>
								<inputType id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.1582482245" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.736729989" name="GNU Arm Cross C++ Compiler" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.compiler.898563844">
								<option id="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.other.1389133801" name="Other compiler flags" superClass="ilg.gnuarmeclipse.managedbuild.cross.option.cpp.compiler.other" value="-w" valueType="string"/>
							</tool>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.20219976" name="GNU Arm Cross C Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.c.linker.1346517925"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.1299774549" name="GNU Arm Cross C++ Linker" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.cpp.linker.1364339306"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.1315013047" name="GNU Arm Cross Archiver" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.archiver.156338365"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.1025419335" name="GNU Arm Cross Create Flash Image" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createflash.34423287"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.1813569847" name="GNU Arm Cross Create Listing" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.createlisting.1711085275"/>
							<tool id="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.1432855572" name="GNU Arm Cross Print Size" superClass="ilg.gnuarmeclipse.managedbuild.cross.tool.printsize.1347463793"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="ra"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="ra_gen"/>
						<entry flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name="src"/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="com.renesas.cdt.managedbuild.core.toolchainInfo"/>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="Ability_Assessment_Project.com.renesas.cdt.managedbuild.gnuarm.target.elf.696753987" name="可执行文件" projectType="com.renesas.cdt.managedbuild.gnuarm.target.elf"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********;com.renesas.cdt.managedbuild.gnuarm.config.elf.release.**********.;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.521544308;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.588509767">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
		<scannerConfigBuildInfo instanceId="com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638;com.renesas.cdt.managedbuild.gnuarm.config.elf.debug.805052638.;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.566306915;ilg.gnuarmeclipse.managedbuild.cross.tool.c.compiler.input.840314022">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="Debug">
			<resource resourceType="PROJECT" workspacePath="/Ability_Assessment_Project"/>
		</configuration>
		<configuration configurationName="Release">
			<resource resourceType="PROJECT" workspacePath="/Ability_Assessment_Project"/>
		</configuration>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.internal.ui.text.commentOwnerProjectMappings"/>
</cproject>