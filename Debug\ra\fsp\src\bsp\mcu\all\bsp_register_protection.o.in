-mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -D_RENESAS_RA_ -D_RA_CORE=CM33 -D_RA_ORDINAL=1 -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/src" -I"." -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc" -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/api" -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/fsp/inc/instances" -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra/arm/CMSIS_6/CMSIS/Core/Include" -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_gen" -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg/bsp" -I"D:/Qian/e2studioa_code/Ability_Assessment_Project/ra_cfg/fsp_cfg" -std=c99 -MMD -MP -MF"ra/fsp/src/bsp/mcu/all/bsp_register_protection.d" -MT"ra/fsp/src/bsp/mcu/all/bsp_register_protection.o" -c -o "ra/fsp/src/bsp/mcu/all/bsp_register_protection.o" -x c "../ra/fsp/src/bsp/mcu/all/bsp_register_protection.c"
