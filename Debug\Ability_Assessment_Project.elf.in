-mcpu=cortex-m33 -mthumb -mfloat-abi=hard -mfpu=fpv5-sp-d16 -O2 -fmessage-length=0 -fsigned-char -ffunction-sections -fdata-sections -fno-strict-aliasing -Wunused -Wuninitialized -Wall -Wextra -Wmissing-declarations -Wconversion -Wpointer-arith -Wshadow -Wlogical-op -Waggregate-return -Wfloat-equal -g -T "fsp.ld" -Xlinker --gc-sections -L "D:\\Qian\\e2studioa_code\\Ability_Assessment_Project/script" -Wl,-Map,"Ability_Assessment_Project.map" --specs=nano.specs -u _printf_float -u _scanf_float -o "Ability_Assessment_Project.elf" -Wl,--start-group ./src/time/gpt0_timing.o ./src/time/gpt6_pwm.o  ./src/systick/systick.o  ./src/scheduler/scheduler.o  ./src/pcf8591/pcf8591.o  ./src/circular_queue/circular_queue.o  ./src/app/led/led_app.o  ./src/app/key/key_app.o  ./src/app/debug_uart/debug_uart.o  ./src/app/adc/adc_app.o  ./src/hal_entry.o ./src/task.o  ./ra_gen/common_data.o ./ra_gen/hal_data.o ./ra_gen/main.o ./ra_gen/pin_data.o ./ra_gen/vector_data.o  ./ra/fsp/src/r_sci_uart/r_sci_uart.o  ./ra/fsp/src/r_ioport/r_ioport.o  ./ra/fsp/src/r_gpt/r_gpt.o  ./ra/fsp/src/r_adc/r_adc.o  ./ra/fsp/src/bsp/mcu/all/bsp_clocks.o ./ra/fsp/src/bsp/mcu/all/bsp_common.o ./ra/fsp/src/bsp/mcu/all/bsp_delay.o ./ra/fsp/src/bsp/mcu/all/bsp_group_irq.o ./ra/fsp/src/bsp/mcu/all/bsp_guard.o ./ra/fsp/src/bsp/mcu/all/bsp_io.o ./ra/fsp/src/bsp/mcu/all/bsp_irq.o ./ra/fsp/src/bsp/mcu/all/bsp_macl.o ./ra/fsp/src/bsp/mcu/all/bsp_register_protection.o ./ra/fsp/src/bsp/mcu/all/bsp_rom_registers.o ./ra/fsp/src/bsp/mcu/all/bsp_sbrk.o ./ra/fsp/src/bsp/mcu/all/bsp_sdram.o ./ra/fsp/src/bsp/mcu/all/bsp_security.o  ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/startup.o ./ra/fsp/src/bsp/cmsis/Device/RENESAS/Source/system.o    -Wl,--end-group
