#include "task.h"

/* 电位器电压值上报 */
void adc_task(void)
{
    double a0 = 0;
    a0 = Read_ADC_Voltage_Value(); // 获取电位器电压值
    my_printf(&g_uart4_ctrl,"a0 == %.2fV\r\n",a0);
}

/* 定时器0计时溢出事件 -> gpt0_timing.c文件 */

/* PWM占空比设置api -> gpt6_pwm_setduty() */
// PWM输出引脚 -> P600

/* led任务函数 */
void led_task(void)
{
/*
    R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_00, BSP_IO_LEVEL_LOW); //LED1亮
    R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_03, BSP_IO_LEVEL_LOW); //LED2亮
    R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_04, BSP_IO_LEVEL_LOW); //LED3亮
    R_BSP_SoftwareDelay(1, BSP_DELAY_UNITS_SECONDS); //延时1秒
    R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_00, BSP_IO_LEVEL_HIGH); //LED1灭
    R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_03, BSP_IO_LEVEL_HIGH); //LED2灭
    R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_04, BSP_IO_LEVEL_HIGH); //LED3灭
    R_BSP_SoftwareDelay(1, BSP_DELAY_UNITS_SECONDS); //延时1秒
*/
    /*
     * 相关宏定义
     * LED1_ON
     * LED1_OFF
     * LED1_TOGGLE
     */
}

/* key任务函数 */
void key_task(void)
{
    key_val = key_read();
    key_down = key_val ^ (key_val & key_old);
    key_up = ~key_val ^ (key_val & key_old);
    key_old = key_val;

    //sw2_long_flag = sw3_long_flag = false;
    if(key_down == 1)
    {
        my_printf(&g_uart4_ctrl,"sw2_down\r\n");
        sw2_long_flag = true;
    }
    if(key_down == 2)
    {
        my_printf(&g_uart4_ctrl,"sw3_down\r\n");
        sw3_long_flag = true;
    }

    if(key_up == 1)
    {
        my_printf(&g_uart4_ctrl,"sw2_up\r\n");
        sw2_long_flag = false;
        if(sw2_time >= 1000)
        {
            // sw2长按
            LED1_ON;
        }
        else
        {
            // sw2短按
            LED2_ON;
        }
    }
    else if(key_up == 2)
    {
        my_printf(&g_uart4_ctrl,"sw3_up\r\n");
        sw3_long_flag = false;
        if(sw3_time >= 1000)
        {
            // sw3长按
            LED1_OFF;
        }
        else
        {
            // sw3短按
            LED2_OFF;
        }
    }
}

/* 串口4任务函数 */
void debug_uart4_task(void)
{
    if (Queue_isEmpty(&Circular_queue_uart4) == false) // 判断队列中的数据不为空
    {
        uart4_Read_Length = Queue_HadUse(&Circular_queue_uart4);
        memset(uart4_Read_Buffer, 0, DATA_LEN);
        Queue_Read(&Circular_queue_uart4, uart4_Read_Buffer, uart4_Read_Length);
        /* 数据解析区 */


        my_printf(&g_uart4_ctrl, "uart4: %s\r\n", uart4_Read_Buffer);
    }
}

