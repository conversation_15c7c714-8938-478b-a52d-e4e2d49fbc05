#include "task.h"

/* 电位器电压值上报 */
void adc_task(void)
{
    double a0 = 0;
    a0 = Read_ADC_Voltage_Value(); // 获取电位器电压值
    if (a0 >= 3.00)
        // 蜂鸣器开始叫
        beep_on();
    else
        beep_off();
    my_printf(&g_uart4_ctrl, "a0 == %.2fV\r\n", a0);
}

/* 定时器0计时溢出事件 -> gpt0_timing.c文件 */

/* PWM占空比设置api -> gpt6_pwm_setduty() */
// PWM输出引脚 -> P600

/* led任务函数 */
void led_task(void)
{
    /*
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_00, BSP_IO_LEVEL_LOW); //LED1亮
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_03, BSP_IO_LEVEL_LOW); //LED2亮
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_04, BSP_IO_LEVEL_LOW); //LED3亮
        R_BSP_SoftwareDelay(1, BSP_DELAY_UNITS_SECONDS); //延时1秒
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_00, BSP_IO_LEVEL_HIGH); //LED1灭
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_03, BSP_IO_LEVEL_HIGH); //LED2灭
        R_IOPORT_PinWrite(&g_ioport_ctrl, BSP_IO_PORT_04_PIN_04, BSP_IO_LEVEL_HIGH); //LED3灭
        R_BSP_SoftwareDelay(1, BSP_DELAY_UNITS_SECONDS); //延时1秒
    */
    /*
     * 相关宏定义
     * LED1_ON
     * LED1_OFF
     * LED1_TOGGLE
     */
}

/* key任务函数 */
void key_task(void)
{
    key_val = key_read();
    key_down = key_val ^ (key_val & key_old);
    key_up = key_old ^ (key_val & key_old);
    key_old = key_val;

    if (key_down == 1)
        sw2_long_flag = true;
    if (key_down == 2)
        sw3_long_flag = true;

    if (key_up == 1)
    {
        sw2_long_flag = false;
        if (sw2_time >= 1000)
        {
            // sw2长按
            LED1_ON;
        }
        else
        {
            // sw2短按
            LED2_ON;
        }
    }
    else if (key_up == 2)
    {
        sw3_long_flag = false;
        if (sw3_time >= 1000)
        {
            // sw3长按
            LED1_OFF;
        }
        else
        {
            // sw3短按
            LED2_OFF;
        }
    }
}

/* 串口4任务函数 */
void debug_uart4_task(void)
{
    if (Queue_isEmpty(&Circular_queue_uart4) == false) // 判断队列中的数据不为空
    {
        uart4_Read_Length = Queue_HadUse(&Circular_queue_uart4);
        memset(uart4_Read_Buffer, 0, DATA_LEN);
        Queue_Read(&Circular_queue_uart4, uart4_Read_Buffer, uart4_Read_Length);
        uart4_Read_Buffer[uart4_Read_Length] = '\0';
        /* 数据解析区 */
        ParsedCommand_t command = parse_uart4_command((const char *)uart4_Read_Buffer);
        switch (command)
        {
        case CMD_LED1_ON:
            LED1_ON;
            my_printf(&g_uart4_ctrl, "Command: LED1 ON received.\r\n");
            break;
        case CMD_LED1_OFF:
            LED1_OFF;
            my_printf(&g_uart4_ctrl, "Command: LED1 OFF received.\r\n");
            break;
        case CMD_UNKNOWN:
        default:
            my_printf(&g_uart4_ctrl, "Command: Unknown command '%s'.\r\n", uart4_Read_Buffer);
            break;
        }
        // my_printf(&g_uart4_ctrl, "uart4: %s\r\n", uart4_Read_Buffer);
    }
}

/**
 * @brief 解析串口接收到的命令字符串。
 *        本函数只负责解析命令，不执行具体操作。
 *        它假定传入的 command_buffer 已经是一个以 null 终止的字符串。
 *        采用前缀匹配方式，增强健壮性。
 *
 * @param command_buffer 串口接收到的命令字符串（必须以 null 终止）。
 * @return ParsedCommand_t 解析出的命令类型。
 */
ParsedCommand_t parse_uart4_command(const char *command_buffer)
{
    // 首先检查传入的字符串是否为空指针，避免空指针解引用
    if (command_buffer == NULL)
    {
        return CMD_UNKNOWN;
    }

    size_t buffer_len = strlen(command_buffer);

    // --- 新增对 "(led1,X)" 格式的解析 ---
    int led_state = -1;     // 用于存储解析出的 LED 状态 (0 或 1)
    size_t chars_consumed = 0; // 用于存储 sscanf 成功匹配并消耗的字符数

    // 尝试解析 "(led1,%d)" 格式
    // sscanf 返回成功转换的项目数。这里我们期待成功转换一个整数 (%d)，所以返回值为 1。
    // %n 是 sscanf 的一个特殊格式符，它会将到目前为止解析的字符数存储到对应的变量 (chars_consumed) 中。
    // 我们可以用 chars_consumed 来确保整个命令字符串都被解析，没有多余的字符。
    if (sscanf(command_buffer, "(led1,%d)%n", &led_state, &chars_consumed) == 1)
    {
        // 确保整个 buffer 都被 sscanf 成功解析了 (即没有额外字符)
        // 并且解析出的 led_state 是 0 或 1
        if (chars_consumed == buffer_len)
        {
            if (led_state == 1)
            {
                return CMD_LED1_ON;
            }
            else if (led_state == 0) // 扩展支持 (led1,0) 为关闭命令
            {
                return CMD_LED1_OFF;
            }
            // 如果 led_state 既不是 0 也不是 1，则继续尝试其他匹配方式 (或返回未知)
        }
    }

    // 定义要匹配的命令字符串及其长度
    const char *cmd_on = "led1-on";
    size_t len_on = strlen(cmd_on);

    const char *cmd_off = "led1-off";
    size_t len_off = strlen(cmd_off);

    // 使用 strncmp 函数进行前缀匹配。
    // strncmp 返回 0 表示两个字符串的前 n 个字符完全相同。
    // 同时，需要确保 command_buffer 的长度至少与要匹配的命令字符串的长度相等，
    // 以避免 "led" 匹配到 "led1-on" 的情况，虽然 strncmp 本身会处理，
    // 但明确检查可以增加代码可读性和意图。

    // 检查是否以 "led1-on" 开头
    if (buffer_len >= len_on && strncmp(command_buffer, cmd_on, len_on) == 0)
    {
        return CMD_LED1_ON;
    }

    // 检查是否以 "led1-off" 开头
    if (buffer_len >= len_off && strncmp(command_buffer, cmd_off, len_off) == 0)
    {
        return CMD_LED1_OFF;
    }

    // 如果以上都不是，则表示是未知命令
    return CMD_UNKNOWN;
}
