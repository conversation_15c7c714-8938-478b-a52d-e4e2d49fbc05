/*
* Copyright (c) 2020 - 2025 Renesas Electronics Corporation and/or its affiliates
*
* SPDX-License-Identifier: BSD-3-Clause
*/

#ifndef BOARD_SDRAM_H
#define BOARD_SDRAM_H

/***********************************************************************************************************************
 * Macro definitions
 **********************************************************************************************************************/

/***********************************************************************************************************************
 * Typedef definitions
 **********************************************************************************************************************/

/***********************************************************************************************************************
 * Exported global variables
 **********************************************************************************************************************/

/***********************************************************************************************************************
 * Exported global functions (to be accessed by other files)
 **********************************************************************************************************************/

/* DEPRECATED: This is a temporary alias to the new SDRAM support in bsp_sdram.c. It will be removed in FSP v6.0.0.
 * It is only present if the new support has not been enabled. */
#if 1 != BSP_CFG_SDRAM_ENABLED
 #define bsp_sdram_init()    R_BSP_SdramInit(true)
#endif

#endif
