#include "debug_uart.h"

uint16_t uart4_Read_Length = 0;
uint8_t uart4_Read_Buffer[DATA_LEN] = {0};

/* 调试串口 UART4 初始化 */
void debug_uart4_Init(void)
{
   fsp_err_t err = FSP_SUCCESS;

   err = R_SCI_UART_Open (&g_uart4_ctrl, &g_uart4_cfg);
   assert(FSP_SUCCESS == err);
}

// 发送完成标志
volatile bool uart0_send_complete_flag = false;
volatile bool uart0_receive_complete_flag = false;

// 串口中断回调
void debug_uart4_callback(uart_callback_args_t *p_args)
{
    switch (p_args->event)
    {
    case UART_EVENT_RX_CHAR:
        Queue_Wirte(&Circular_queue_uart4, (uint8_t *)&p_args->data, 1);
        break;
    case UART_EVENT_TX_COMPLETE:
        uart0_send_complete_flag = true;
        break;
    default:
        break;
    }
}

/*
void debug_uart4_task(void)
{
    if (Queue_isEmpty(&Circular_queue_uart4) == false) // 判断队列中的数据不为空
    {
        uart4_Read_Length = Queue_HadUse(&Circular_queue_uart4);

        memset(uart4_Read_Buffer, 0, DATA_LEN);

        Queue_Read(&Circular_queue_uart4, uart4_Read_Buffer, uart4_Read_Length);
        my_printf(&g_uart4_ctrl, "uart4: %s\r\n", uart4_Read_Buffer);
    }
}
*/

// 串口重定向到printf (FSP库 for RA6M5)
int my_printf(sci_uart_instance_ctrl_t *uart, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    // 确保format不为NULL
    if (!format)
        return -1;

    // 初始化可变参数列表
    va_start(arg, format);

    // 添加错误检查
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);

    // 确保不超出缓冲区
    if (len < 0 || (uint16_t)len > sizeof(buffer))
    {
        return -1;
    }

    // 发送数据 - 阻塞方式
    fsp_err_t err;
    err = R_SCI_UART_Write(uart, (uint8_t *)buffer, (uint32_t)len);
    if (FSP_SUCCESS != err)
    {
        // 错误处理
        return -1;
    }
    // R_SCI_UART_Write(uart, (uint8_t *)buffer, (uint32_t)len);

    R_BSP_SoftwareDelay(1, BSP_DELAY_UNITS_MILLISECONDS);
    return len;
}
