#include "scheduler.h"

// 任务数量
uint32_t task_num;

typedef struct
{
    void (*task_func)(void); // 任务函数
    uint32_t rate_ms;        // 任务执行周期
    uint32_t last_ms;        // 任务上一次运行时间
} task_t;

static task_t scheduler_task[] =
{
        {led_task, 1, 0},
        {key_task, 10, 0},
        {debug_uart4_task, 10, 0},
        {adc_task,1000,0},
};

void scheduler_init(void)
{
    task_num = sizeof(scheduler_task) / sizeof(task_t);
    led_init(); // led初始化
    //Key_IRQ_Init(); // 按键外部中断初始化
    key_init();
    debug_uart4_Init(); // 调试串口4初始化
    adc_init(); // adc测量初始化
    gpt0_timing_init(); // 定时器为1s一次中断
    gpt6_pwm_init(); // PWM输出引脚为P600
}

void scheduler_run()
{
    for (uint8_t i = 0; i < task_num; i++)
    {
        uint32_t now_time = uwTick;
        if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_ms)
        {
            scheduler_task[i].last_ms = now_time;
            scheduler_task[i].task_func();
        }
    }
}
