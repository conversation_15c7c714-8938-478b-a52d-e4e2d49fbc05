<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<launchConfiguration type="com.renesas.cdt.launch.dsf.gdbremote.launchConfigurationType">
    <booleanAttribute key=".setStepMode" value="false"/>
    <intAttribute key="com.renesas.cdt.core.connectionTimeout" value="30"/>
    <stringAttribute key="com.renesas.cdt.core.initCommands" value=""/>
    <stringAttribute key="com.renesas.cdt.core.ipAddress" value="localhost"/>
    <stringAttribute key="com.renesas.cdt.core.jtagDeviceId" value="com.renesas.hardwaredebug.rz.jlink"/>
    <listAttribute key="com.renesas.cdt.core.listGDBExe"/>
    <listAttribute key="com.renesas.cdt.core.listGDBLaunchName">
        <listEntry value="main"/>
    </listAttribute>
    <listAttribute key="com.renesas.cdt.core.listGDBPort">
        <listEntry value="61234"/>
    </listAttribute>
    <stringAttribute key="com.renesas.cdt.core.optionInitCommands" value="monitor force_rtos_off&#10;&#10;"/>
    <intAttribute key="com.renesas.cdt.core.portNumber" value="61234"/>
    <stringAttribute key="com.renesas.cdt.core.runCommands" value=""/>
    <stringAttribute key="com.renesas.cdt.core.secondGDBExe" value="green_dsp-elf-gdb"/>
    <intAttribute key="com.renesas.cdt.core.secondGdbPortNumber" value="61237"/>
    <stringAttribute key="com.renesas.cdt.core.serverParam" value="-g SEGGERJLINKARM -t R7FA6M5BH -uConnectionTimeout= 30 -uSelect= &quot;USB&quot; -uJLinkSetting= &quot;${workspace_loc:/${ProjName}}/${LaunchConfigName}.jlink&quot; -uJLinkLog= &quot;${workspace_loc:/${ProjName}}/JLinkLog.log&quot; -uLowPower= 0 -uInteface= &quot;SWD&quot; -uIfSpeed= &quot;4000&quot; -uResetBeginConnection= 1 -uNoReset= 1 -uResetBefDownload= 1 -uResetCon= 1 -uReleaseCM3= 0 -uDisconnectionMode= &quot;2&quot; -uSWOcoreClock= &quot;0&quot; -uEnableSciBoot=  1 -uresetOnReload= 1 -n &quot;0&quot; -uFlashBp= 1 -uSimulation= 0 -ueraseRomOnDownload= 0 -ueraseDataRomOnDownload= 0 -uOSRestriction= 0 -uAllowCachingFlash= 0 -uCPUFrequency= &quot;0&quot; -uCECycle= 1 -uResetBehavior= &quot;Reset&quot;"/>
    <booleanAttribute key="com.renesas.cdt.core.setResume" value="true"/>
    <stringAttribute key="com.renesas.cdt.core.targetDevice" value="R7FA6M5BH"/>
    <booleanAttribute key="com.renesas.cdt.core.useRemoteTarget" value="true"/>
    <stringAttribute key="com.renesas.cdt.debug.ioview.dsf.registerSelection0" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&#13;&#10;&lt;selectedRegisterList ioFilePath=&quot;C:\Users\<USER>\.eclipse\com.renesas.platform_1309835749\DebugComp\RA\IoFiles\R7FA6M5BH.svd&quot;/&gt;&#13;&#10;"/>
    <stringAttribute key="com.renesas.cdt.launch.dsf.IO_MAP" value="${support_area_loc}"/>
    <booleanAttribute key="com.renesas.cdt.launch.dsf.USE_DEFAULT_IO_MAP" value="true"/>
    <stringAttribute key="com.renesas.cdt.launch.dsf.launchSeqType" value="com.renesas.cdt.launch.dsf.launchSequence.e2GdbServer"/>
    <stringAttribute key="com.renesas.cdt.launch.dsf.serverPath" value="${renesas.support.targetLoc:com.renesas.ide.supportfiles.ra.debug.debugSupportFileTarget}\e2-server-gdb"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.break.allowSimulation" value="false"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.break.useFlashBreakpoints.resetorrepurposed" value="true"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.cfiFlash.flashBusType" value="SPIBSC"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.cfiFlash.flashMemoryType" value="SerialFlash"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.disconnectionMode" value="2"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.id_code2" value="FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.jlinkScript" value=""/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.registerInit" value="false"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.reset" value="false"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.resetAfterDownload" value="true"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.resetBeginConnection" value="true"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.resetCon" value="true"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.resetPreRun" value="true"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.setArMModeAfterDownload" value="false"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.connection.swv.coreClockSpeed" value="0"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.enable.hot.plug" value="false"/>
    <intAttribute key="com.renesas.hardwaredebug.arm.jlink.interface.speed" value="4000"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.interface.type" value="SWD"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.jlink.logFile" value="${workspace_loc:/${ProjName}}/JLinkLog.log"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.jlink.lowPowerHandling" value="No"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.jlink.scriptFile" value=""/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.jlink.settingsFile" value="${workspace_loc:/${ProjName}}/${LaunchConfigName}.jlink"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.memory.isLittleEndian" value="true"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.nonSecureVectorAddress" value=""/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.options.ArmJLinkDebugToolSettingsTree.prog_rewrite_irom" value="false"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.options.ArmJLinkDebugToolSettingsTree.resetAfterReload" value="true"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.options.ArmJLinkDebugToolSettingsTree.rtosIntegrationInDebugView" value="false"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.options.ArmJLinkDebugToolSettingsTree.rtosintegrationthreadsrunning" value="false"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.release.reset" value="true"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.reset.PcOnReset" value=""/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.reset.behaviorOfReset" value="Reset"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.reset.setCpsrOnReset" value=""/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.scanChain.multiDevices" value="false"/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.secureVectorAddress" value=""/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.semihosting.breakpointAddress" value=""/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.setTZAuthKey" value=""/>
    <stringAttribute key="com.renesas.hardwaredebug.arm.jlink.setTZAuthLevel" value="0"/>
    <booleanAttribute key="com.renesas.hardwaredebug.arm.jlink.setTZBoundaries" value="true"/>
    <booleanAttribute key="com.renesas.hardwaredebug.timemeasurement" value="true"/>
    <intAttribute key="org.eclipse.cdt.debug.gdbjtag.core.delay" value="3"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.doHalt" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.doReset" value="false"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.imageFileName" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.imageOffset" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.initCommands" value=""/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.loadImage" value="true"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.loadSymbols" value="true"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.pcRegister" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.runCommands" value=""/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.setPcRegister" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.setResume" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.setStopAt" value="true"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.stopAt" value="main"/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.symbolsFileName" value=""/>
    <stringAttribute key="org.eclipse.cdt.debug.gdbjtag.core.symbolsOffset" value=""/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useFileForImage" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useFileForSymbols" value="false"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useProjBinaryForImage" value="true"/>
    <booleanAttribute key="org.eclipse.cdt.debug.gdbjtag.core.useProjBinaryForSymbols" value="true"/>
    <stringAttribute key="org.eclipse.cdt.dsf.gdb.DEBUG_NAME" value="arm-none-eabi-gdb"/>
    <booleanAttribute key="org.eclipse.cdt.dsf.gdb.NON_STOP" value="true"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROGRAM_NAME" value="Debug/Ability_Assessment_Project.elf"/>
    <stringAttribute key="org.eclipse.cdt.launch.PROJECT_ATTR" value="Ability_Assessment_Project"/>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_PATHS">
        <listEntry value="/Ability_Assessment_Project"/>
    </listAttribute>
    <listAttribute key="org.eclipse.debug.core.MAPPED_RESOURCE_TYPES">
        <listEntry value="4"/>
    </listAttribute>
    <booleanAttribute key="org.eclipse.debug.ui.ATTR_LAUNCH_IN_BACKGROUND" value="false"/>
    <stringAttribute key="org.eclipse.dsf.launch.MEMORY_BLOCKS" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;&lt;memoryBlockExpressionList context=&quot;reserved-for-future-use&quot;/&gt;"/>
    <stringAttribute key="process_factory_id" value="org.eclipse.cdt.dsf.gdb.GdbProcessFactory"/>
</launchConfiguration>
