#include "gpt0_timing.h"

/* GPT0 初始化函数 */
void gpt0_timing_init(void)
{
    /* 初始化 GPT0 模块 */
    R_GPT_Open(&g_timer0_ctrl, &g_timer0_cfg);

    /* 启动 GPT0 定时器 */
    R_GPT_Start(&g_timer0_ctrl);
}

/* GPT0 中断回调函数 */
void gpt0_timing_callback(timer_callback_args_t * p_args)
{
    /* 定时器溢出事件 */
    if (TIMER_EVENT_CYCLE_END == p_args->event)
    {
        /* 翻转 LED1 */
        LED3_TOGGLE;    //每秒翻转一次
    }
}

