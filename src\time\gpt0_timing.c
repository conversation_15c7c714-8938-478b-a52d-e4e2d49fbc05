#include "gpt0_timing.h"

/* GPT0 初始化函数 */
void gpt0_timing_init(void)
{
    /* 初始化 GPT0 模块 */
    R_GPT_Open(&g_timer0_ctrl, &g_timer0_cfg);

    /* 启动 GPT0 定时器 */
    R_GPT_Start(&g_timer0_ctrl);
}

/* GPT0 中断回调函数 */
void gpt0_timing_callback(timer_callback_args_t * p_args)
{
    /* 定时器溢出事件 */
    if (TIMER_EVENT_CYCLE_END == p_args->event)
    {
        /* 翻转 LED1 */
        LED3_TOGGLE;    //每秒翻转一次
    }
}
/*
    Channel -> 通道选择
    Mode -> 模式选择
        Periodic -> 周期计数
        One-Shot -> 单次计数
        PWM -> PWM模式
        One-Shot Pulse -> 三角波对称PWM模式
        Triangle-Wave Symmetric PWM -> 三角波对称PWM模式
        Triangle-Wave Asymmetric PWM -> 三角波不对称PWM模式
        Triangle-Wave Asymmetric PWM (Mode 3) -> 三角波不对称PWM模式（模式3）
    Period -> 计数器的计数周期。
    Period Unit -> 计数器计数周期的单位。
*/

