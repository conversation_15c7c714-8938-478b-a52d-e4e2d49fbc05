#ifndef APP_KEY_KEY_APP_H_
#define APP_KEY_KEY_APP_H_

#include "mydefine.h"

#define KEY1_SW2_PIN    BSP_IO_PORT_00_PIN_04
#define KEY2_SW3_PIN    BSP_IO_PORT_00_PIN_05

/* 单个引脚的电平和读取电平 */
typedef enum io_level
 {
     IO_LEVEL_LOW = 0,              // Low
     IO_LEVEL_HIGH                  // High
 } io_level_t;

 void key_init(void);
 uint8_t key_read(void);

//void Key_IRQ_Init(void);
//void key_task(void);

/*extern volatile bool key1_sw2_press;
extern volatile bool key2_sw3_press;

extern volatile bool key1_sw2_press_long = false;
extern volatile bool key2_sw3_press_long = false;*/

extern uint8_t key_val,key_down,key_up,key_old;
extern bool sw2_long_flag;
extern bool sw3_long_flag;
extern uint16_t sw2_time;
extern uint16_t sw3_time;

#endif /* APP_KEY_KEY_APP_H_ */
