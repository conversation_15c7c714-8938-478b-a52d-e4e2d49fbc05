#include "pcf8591.h"

/* PCF8591初始化 */
void pcf8591_init(void)
{
    my_iic_init();
}

/* 读取PCF8591 ADC值 */
uint8_t pcf8591_read_adc(uint8_t channel)
{
    uint8_t control_byte, adc_value;

    if(channel > 3)
        return 0;

    control_byte = channel;

    // 写入控制字节
    my_iic_start();
    my_iic_send_byte(PCF8591_ADDR << 1 | MY_IIC_WRITE);
    if(my_iic_wait_ack())
    {
        my_iic_stop();
        return 0;
    }

    my_iic_send_byte(control_byte);
    if(my_iic_wait_ack())
    {
        my_iic_stop();
        return 0;
    }

    // 读取ADC值(需要读两次,第一次是上次转换结果)
    my_iic_start();
    my_iic_send_byte(PCF8591_ADDR << 1 | MY_IIC_READ);
    if(my_iic_wait_ack())
    {
        my_iic_stop();
        return 0;
    }

    my_iic_read_byte(); // 丢弃第一次读取
    my_iic_send_ack();

    adc_value = my_iic_read_byte(); // 获取实际ADC值
    my_iic_send_nack();

    my_iic_stop();
    return adc_value;
}

/* 写入PCF8591 DAC值 */
void pcf8591_write_dac(uint8_t value)
{
    uint8_t control_byte = PCF8591_AOUT_EN; // 使能DAC输出

    my_iic_start();
    my_iic_send_byte(PCF8591_ADDR << 1 | MY_IIC_WRITE);
    if(my_iic_wait_ack())
    {
        my_iic_stop();
        return;
    }

    my_iic_send_byte(control_byte);
    if(my_iic_wait_ack())
    {
        my_iic_stop();
        return;
    }

    my_iic_send_byte(value);
    if(my_iic_wait_ack())
    {
        my_iic_stop();
        return;
    }

    my_iic_stop();
}
