<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="10">
  <generalSettings>
    <option key="#Board#" value="board.custom"/>
    <option key="CPU" value="RA6M5"/>
    <option key="Core" value="CM33"/>
    <option key="#TargetName#" value="R7FA6M5BH3CFC"/>
    <option key="#TargetARCHITECTURE#" value="cortex-m33"/>
    <option key="#DeviceCommand#" value="R7FA6M5BH"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R7FA6M5BH3CFC.pincfg"/>
    <option key="#FSPVersion#" value="5.8.0"/>
    <option key="#SELECTED_TOOLCHAIN#" value="gcc-arm-embedded"/>
    <option key="#ToolchainVersion#" value="10.3.1.20210824"/>
  </generalSettings>
  <raBspConfiguration/>
  <raClockConfiguration>
    <node id="board.clock.xtal.freq" mul="24000000" option="_edit"/>
    <node id="board.clock.hoco.freq" option="board.clock.hoco.freq.20m"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.32768"/>
    <node id="board.clock.moco.freq" option="board.clock.moco.freq.8m"/>
    <node id="board.clock.subclk.freq" option="board.clock.subclk.freq.32768"/>
    <node id="board.clock.pll.source" option="board.clock.pll.source.xtal"/>
    <node id="board.clock.pll.div" option="board.clock.pll.div.3"/>
    <node id="board.clock.pll.mul" option="board.clock.pll.mul.250"/>
    <node id="board.clock.pll.display" option="board.clock.pll.display.value"/>
    <node id="board.clock.pll2.source" option="board.clock.pll2.source.disabled"/>
    <node id="board.clock.pll2.div" option="board.clock.pll2.div.2"/>
    <node id="board.clock.pll2.mul" option="board.clock.pll2.mul.200"/>
    <node id="board.clock.pll2.display" option="board.clock.pll2.display.value"/>
    <node id="board.clock.clock.source" option="board.clock.clock.source.pll"/>
    <node id="board.clock.clkout.source" option="board.clock.clkout.source.disabled"/>
    <node id="board.clock.uclk.source" option="board.clock.uclk.source.disabled"/>
    <node id="board.clock.u60ck.source" option="board.clock.u60ck.source.disabled"/>
    <node id="board.clock.octaspiclk.source" option="board.clock.octaspiclk.source.disabled"/>
    <node id="board.clock.canfdclk.source" option="board.clock.canfdclk.source.disabled"/>
    <node id="board.clock.cecclk.source" option="board.clock.cecclk.source.disabled"/>
    <node id="board.clock.iclk.div" option="board.clock.iclk.div.1"/>
    <node id="board.clock.pclka.div" option="board.clock.pclka.div.2"/>
    <node id="board.clock.pclkb.div" option="board.clock.pclkb.div.4"/>
    <node id="board.clock.pclkc.div" option="board.clock.pclkc.div.4"/>
    <node id="board.clock.pclkd.div" option="board.clock.pclkd.div.2"/>
    <node id="board.clock.bclk.div" option="board.clock.bclk.div.2"/>
    <node id="board.clock.bclkout.div" option="board.clock.bclkout.div.2"/>
    <node id="board.clock.fclk.div" option="board.clock.fclk.div.4"/>
    <node id="board.clock.clkout.div" option="board.clock.clkout.div.1"/>
    <node id="board.clock.uclk.div" option="board.clock.uclk.div.5"/>
    <node id="board.clock.u60ck.div" option="board.clock.u60ck.div.1"/>
    <node id="board.clock.octaspiclk.div" option="board.clock.octaspiclk.div.1"/>
    <node id="board.clock.canfdclk.div" option="board.clock.canfdclk.div.6"/>
    <node id="board.clock.cecclk.div" option="board.clock.cecclk.div.1"/>
    <node id="board.clock.iclk.display" option="board.clock.iclk.display.value"/>
    <node id="board.clock.pclka.display" option="board.clock.pclka.display.value"/>
    <node id="board.clock.pclkb.display" option="board.clock.pclkb.display.value"/>
    <node id="board.clock.pclkc.display" option="board.clock.pclkc.display.value"/>
    <node id="board.clock.pclkd.display" option="board.clock.pclkd.display.value"/>
    <node id="board.clock.bclk.display" option="board.clock.bclk.display.value"/>
    <node id="board.clock.bclkout.display" option="board.clock.bclkout.display.value"/>
    <node id="board.clock.fclk.display" option="board.clock.fclk.display.value"/>
    <node id="board.clock.clkout.display" option="board.clock.clkout.display.value"/>
    <node id="board.clock.uclk.display" option="board.clock.uclk.display.value"/>
    <node id="board.clock.u60ck.display" option="board.clock.u60ck.display.value"/>
    <node id="board.clock.octaspiclk.display" option="board.clock.octaspiclk.display.value"/>
    <node id="board.clock.canfdclk.display" option="board.clock.canfdclk.display.value"/>
    <node id="board.clock.cecclk.display" option="board.clock.cecclk.display.value"/>
  </raClockConfiguration>
  <raPinConfiguration>
    <pincfg active="true" name="" symbol="">
      <configSetting altId="adc0.an00.p000" configurationId="adc0.an00" isUsedByDriver="true" peripheral="ADC0"/>
      <configSetting altId="debug0.swclk.p300" configurationId="debug0.swclk" isUsedByDriver="true" peripheral="DEBUG0"/>
      <configSetting altId="debug0.swdio.p108" configurationId="debug0.swdio" isUsedByDriver="true" peripheral="DEBUG0"/>
      <configSetting altId="gpt6.gtiocb.p600" configurationId="gpt6.gtiocb" isUsedByDriver="true" peripheral="GPT6"/>
      <configSetting altId="p004.input" configurationId="p004"/>
      <configSetting altId="p005.input" configurationId="p005"/>
      <configSetting altId="p400.output.high" configurationId="p400"/>
      <configSetting altId="p403.output.high" configurationId="p403"/>
      <configSetting altId="p404.output.high" configurationId="p404"/>
      <configSetting altId="p604.output.high" configurationId="p604"/>
      <configSetting altId="sci4.rxd.p511" configurationId="sci4.rxd" isUsedByDriver="true" peripheral="SCI4"/>
      <configSetting altId="sci4.txd.p512" configurationId="sci4.txd" isUsedByDriver="true" peripheral="SCI4"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
