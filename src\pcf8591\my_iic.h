/*********************************************************************************************************************
 * Copyright (c) 2024, 逐飞科技
 * All rights reserved.
 *
 * 以下所有内容版权均属逐飞科技所有，未经允许不得用于商业用途，
 * 欢迎各位使用并传播本程序，修改内容时必须保留逐飞科技的版权声明。
 *
 * @file       		my_iic.h
 * @company	   		成都逐飞科技有限公司
 * <AUTHOR>
 * @version    		查看doc内version文件 版本说明
 * @Software 		e2studio
 * @Target core		RA6M5
 * @Taobao   		https://seekfree.taobao.com/
 * @date       		2024-02-18
 * @note
 * 				本文件是软件IIC驱动头文件
 * 				基于GPIO模拟IIC时序，支持标准IIC协议
 * 				适用于与各种IIC设备通信，如PCF8591、EEPROM等
 *
 * 				版本说明：
 * 				v1.0: 初始版本，实现基本IIC功能
 * 				- 支持START/STOP条件生成
 * 				- 支持ACK/NACK应答处理
 * 				- 支持字节发送和接收
 * 				- 支持寄存器读写操作
 * 				- 支持数据块读写操作
 *
 * 				接线说明：
 * 				SCL  ----  P100 (可修改MY_IIC_SCL_PIN宏定义)
 * 				SDA  ----  P101 (可修改MY_IIC_SDA_PIN宏定义)
 * 				VCC  ----  3.3V
 * 				GND  ----  GND
 * 				注意：SCL和SDA需要外接4.7kΩ上拉电阻到VCC
 ********************************************************************************************************************/

#ifndef PCF8591_MY_IIC_H_
#define PCF8591_MY_IIC_H_

#include "mydefine.h"

//====================================================软件IIC 基础配置====================================================
/* IIC引脚宏定义 - 用户可根据实际硬件连接修改 */
#define MY_IIC_SCL_PIN    BSP_IO_PORT_01_PIN_00  // SCL时钟线引脚 P100
#define MY_IIC_SDA_PIN    BSP_IO_PORT_01_PIN_01  // SDA数据线引脚 P101

/* IIC电平定义 - 与BSP层电平定义保持一致 */
#define MY_IIC_HIGH       BSP_IO_LEVEL_HIGH      // 高电平
#define MY_IIC_LOW        BSP_IO_LEVEL_LOW       // 低电平

/* IIC时序参数配置 */
#define MY_IIC_DELAY_US   5                      // IIC时序延时时间(微秒) - 决定通信速度，约100kHz

//====================================================软件IIC 枚举定义====================================================
/* IIC操作状态枚举 - 用于函数返回值，指示操作结果 */
typedef enum {
    MY_IIC_SUCCESS = 0,                          // 操作成功
    MY_IIC_ERROR,                                // 参数错误或其他错误
    MY_IIC_NACK,                                 // 设备无应答(NACK)
    MY_IIC_TIMEOUT                               // 操作超时
} my_iic_status_t;

/* IIC数据传输方向枚举 - 用于指示读写方向 */
typedef enum {
    MY_IIC_WRITE = 0,                            // 写操作 - 主机向从机发送数据
    MY_IIC_READ = 1                              // 读操作 - 主机从从机接收数据
} my_iic_direction_t;

//====================================================软件IIC 对外接口函数====================================================
void            my_iic_init                     (void);
void            my_iic_start                    (void);
void            my_iic_stop                     (void);
void            my_iic_send_ack                 (void);
void            my_iic_send_nack                (void);
uint8_t         my_iic_wait_ack                 (void);
void            my_iic_send_byte                (uint8_t data);
uint8_t         my_iic_read_byte                (void);
my_iic_status_t my_iic_write_reg                (uint8_t device_addr, uint8_t reg_addr, uint8_t data);
my_iic_status_t my_iic_read_reg                 (uint8_t device_addr, uint8_t reg_addr, uint8_t *data);
my_iic_status_t my_iic_write_data               (uint8_t device_addr, uint8_t *data, uint16_t len);
my_iic_status_t my_iic_read_data                (uint8_t device_addr, uint8_t *data, uint16_t len);

//====================================================软件IIC 内部函数声明====================================================
// 注意：以下函数为内部使用，用户无需直接调用
static void     my_iic_scl_high                 (void);                         // SCL引脚输出高电平
static void     my_iic_scl_low                  (void);                         // SCL引脚输出低电平
static void     my_iic_sda_high                 (void);                         // SDA引脚输出高电平
static void     my_iic_sda_low                  (void);                         // SDA引脚输出低电平
static void     my_iic_sda_input                (void);                         // SDA引脚配置为输入模式
static void     my_iic_sda_output               (void);                         // SDA引脚配置为输出模式
static uint8_t  my_iic_sda_read                 (void);                         // 读取SDA引脚电平状态
static void     my_iic_delay_us                 (uint32_t us);                  // 微秒级延时函数

#endif /* PCF8591_MY_IIC_H_ */
