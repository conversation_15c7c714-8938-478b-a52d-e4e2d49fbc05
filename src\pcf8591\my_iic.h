#ifndef PCF8591_MY_IIC_H_
#define PCF8591_MY_IIC_H_

#include "mydefine.h"

/* IIC引脚宏定义 */
#define MY_IIC_SCL_PIN    BSP_IO_PORT_01_PIN_00  // SCL引脚 P100
#define MY_IIC_SDA_PIN    BSP_IO_PORT_01_PIN_01  // SDA引脚 P101

/* IIC电平定义 */
#define MY_IIC_HIGH       BSP_IO_LEVEL_HIGH
#define MY_IIC_LOW        BSP_IO_LEVEL_LOW

/* IIC延时时间(us) */
#define MY_IIC_DELAY_US   5

/* IIC状态定义 */
typedef enum {
    MY_IIC_SUCCESS = 0,
    MY_IIC_ERROR,
    MY_IIC_NACK,
    MY_IIC_TIMEOUT
} my_iic_status_t;

/* IIC方向定义 */
typedef enum {
    MY_IIC_WRITE = 0,
    MY_IIC_READ = 1
} my_iic_direction_t;

/* 函数声明 */
void my_iic_init(void);
void my_iic_start(void);
void my_iic_stop(void);
void my_iic_send_ack(void);
void my_iic_send_nack(void);
uint8_t my_iic_wait_ack(void);
void my_iic_send_byte(uint8_t data);
uint8_t my_iic_read_byte(void);
my_iic_status_t my_iic_write_reg(uint8_t device_addr, uint8_t reg_addr, uint8_t data);
my_iic_status_t my_iic_read_reg(uint8_t device_addr, uint8_t reg_addr, uint8_t *data);
my_iic_status_t my_iic_write_data(uint8_t device_addr, uint8_t *data, uint16_t len);
my_iic_status_t my_iic_read_data(uint8_t device_addr, uint8_t *data, uint16_t len);

/* 内部函数声明 */
static void my_iic_scl_high(void);
static void my_iic_scl_low(void);
static void my_iic_sda_high(void);
static void my_iic_sda_low(void);
static void my_iic_sda_input(void);
static void my_iic_sda_output(void);
static uint8_t my_iic_sda_read(void);
static void my_iic_delay_us(uint32_t us);

#endif /* PCF8591_MY_IIC_H_ */
