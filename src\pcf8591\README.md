# 软件IIC驱动说明

## 概述
本模块实现了基于GPIO的软件IIC驱动，用于与PCF8591等IIC设备通信。

## 文件结构
- `my_iic.h` - 软件IIC底层驱动头文件
- `my_iic.c` - 软件IIC底层驱动实现
- `pcf8591.h` - PCF8591设备驱动头文件  
- `pcf8591.c` - PCF8591设备驱动实现

## 引脚配置
```c
#define MY_IIC_SCL_PIN    BSP_IO_PORT_01_PIN_00  // SCL引脚 P100
#define MY_IIC_SDA_PIN    BSP_IO_PORT_01_PIN_01  // SDA引脚 P101
```

## 使用方法

### 1. 初始化
```c
my_iic_init();        // 初始化IIC总线
pcf8591_init();       // 初始化PCF8591设备
```

### 2. 基本IIC操作
```c
// 写寄存器
my_iic_status_t status = my_iic_write_reg(0x48, 0x00, 0xFF);

// 读寄存器
uint8_t data;
my_iic_status_t status = my_iic_read_reg(0x48, 0x00, &data);

// 写数据块
uint8_t write_data[] = {0x01, 0x02, 0x03};
my_iic_status_t status = my_iic_write_data(0x48, write_data, 3);

// 读数据块
uint8_t read_data[3];
my_iic_status_t status = my_iic_read_data(0x48, read_data, 3);
```

### 3. PCF8591操作
```c
// 读取ADC值
uint8_t adc_value = pcf8591_read_adc(PCF8591_AIN0);  // 读取通道0

// 输出DAC值
pcf8591_write_dac(128);  // 输出中间电压
```

## 状态码说明
- `MY_IIC_SUCCESS` - 操作成功
- `MY_IIC_ERROR` - 参数错误
- `MY_IIC_NACK` - 设备无应答
- `MY_IIC_TIMEOUT` - 操作超时

## 注意事项
1. 使用前需要在硬件上连接上拉电阻(4.7kΩ)
2. 时序延时可根据实际需要调整`MY_IIC_DELAY_US`
3. 引脚配置需要根据实际硬件连接修改
4. PCF8591地址默认为0x48，可根据硬件配置修改

## 性能参数
- 通信速度：约100kHz(可调整)
- 支持标准IIC协议
- 支持多字节读写
- 自动处理START/STOP条件
