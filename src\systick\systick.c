#include "systick.h"

static __IO uint32_t IT_nums; // 延时需要触发中断的次数
static uint32_t IT_Period;    // 中断周期
uint32_t uwTick = 0; // 系统时钟计数

// 系统定时器初始化
// 第一个参数为定时器中断频率，单位为Hz
void SysTick_Init(uint32_t IT_frequency)
{
    /* SystemCoreClock在这里默认为200M
     * SystemCoreClock / 1000    1ms中断一次
     * SystemCoreClock / 100000  10us中断一次
     * SystemCoreClock / 1000000 1us中断一次
     */
    IT_Period = SystemCoreClock / IT_frequency;
    uint32_t err = SysTick_Config(IT_Period);
    assert(err == 0); // capture error
}

// 延时函数
// 第一个参数为延时时间
// 第二个参数为时钟节拍数，单位为ms或us
void SysTick_Delay(uint32_t delay, sys_delay_units_t unit)
{
    uint32_t SumTime = delay * unit; // 计算总延时时间(单位为时钟节拍数)
    IT_nums = SumTime / IT_Period;
    SysTick->VAL = 0UL;
    while (IT_nums != 0)
        ;
}

// 系统定时器中断服务函数
extern void SysTick_Handler(void); // 需要先extern声明一下避免编译器警告
void SysTick_Handler(void)
{
    if (IT_nums != 0x00)
    {
        IT_nums--;
    }
    uwTick++;
    // sw2长按计时
    if(sw2_long_flag == true)
        sw2_time++;
    else
        sw2_time = 0;

    // sw3长按计时
    if(sw3_long_flag == true)
        sw3_time++;
    else
        sw3_time = 0;
}
